import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart' as material;
import 'package:http/http.dart' as http;
import 'package:flutter/widgets.dart';
import 'package:flutter/foundation.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../../../core/config/app_config.dart';
import '../../../core/widgets/background.dart';
import './test_data.dart';
import './unit_and_subtopic_selection.dart';

class DynamicTestDisplay extends ConsumerStatefulWidget {
  const DynamicTestDisplay({super.key});

  @override
  ConsumerState<DynamicTestDisplay> createState() => _DynamicTestDisplayState();
}

class _DynamicTestDisplayState extends ConsumerState<DynamicTestDisplay> {
  int _currentIndex = 0;
  Map<String, String> _userAnswers = {};
  int _timeLeft = 3600;
  bool _isSubmitting = false;
  bool _showFeedback = false;
  FeedbackData? _feedbackData;
  Timer? _timer;

  Widget _renderOptions(List<String> options, String? selectedOption, String questionId) {
    return Column(
      children: options.asMap().entries.map((entry) {
        final option = entry.value;
        return material.RadioListTile<String>(
          value: option,
          groupValue: selectedOption,
          onChanged: (value) => _handleOptionChange(value!, questionId),
          title: material.Text(
            option,
            style: const material.TextStyle(fontSize: 16),
            overflow: material.TextOverflow.ellipsis,
            maxLines: 2,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildMetricCard(String title, String icon, material.Color color) {
    return material.Expanded(
      child: material.Card(
        color: color,
        child: material.Padding(
          padding: const material.EdgeInsets.all(8.0),
          child: material.Row(
            mainAxisSize: material.MainAxisSize.min,
            children: [
              material.Text(icon, style: const material.TextStyle(fontSize: 20)),
              const material.SizedBox(width: 4),
              material.Flexible(
                child: material.Text(
                  title,
                  style: const material.TextStyle(
                    fontSize: 14,
                    fontWeight: material.FontWeight.bold,
                  ),
                  overflow: material.TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _initializeTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _initializeTimer() {
    final testData = ref.read(testProvider);
    debugPrint('Initializing timer, testData: ${testData?.examId}');
    if (testData?.testDuration != null) {
      final duration = testData!.testDuration!;
      if (duration.contains('hour')) {
        final hours = double.tryParse(duration.split(' ')[0]) ?? 1;
        _timeLeft = (hours * 3600).round();
      } else if (duration.contains('minute')) {
        final minutes = double.tryParse(duration.split(' ')[0]) ?? 60;
        _timeLeft = (minutes * 60).round();
      } else if (double.tryParse(duration) != null) {
        _timeLeft = int.parse(duration);
      }
    } else {
      _timeLeft = 30 * 60;
    }
    if (_timeLeft > 0) {
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          _timeLeft--;
          if (_timeLeft <= 0 && !_isSubmitting && !_showFeedback) {
            _handleSubmitTest();
          }
        });
      });
    }
  }

  void _handleOptionChange(String option, String questionId) {
    setState(() {
      _userAnswers[questionId] = option;
    });
  }

  void _handleNext() {
    setState(() => _currentIndex++);
  }

  void _handlePrevious() {
    setState(() => _currentIndex--);
  }

  Future<void> _handleSubmitTest() async {
    setState(() => _isSubmitting = true);
    final testData = ref.read(testProvider)!;
    try {
      final formattedAnswers = _userAnswers.entries
          .map((e) => {'question_id': e.key, 'selected_option': e.value})
          .toList();

      final payload = {
        'exam_id': testData.examId,
        'user_id': testData.userId,
        'answers': formattedAnswers,
      };

      final subject = testData.subject.trim().toLowerCase();
      String endpoint;
      if (['physics'].contains(subject)) {
        endpoint = '${AppConfig.apiBaseUrl}/evaluate-test-physics';
      } else if (['chemistry'].contains(subject)) {
        endpoint = '${AppConfig.apiBaseUrl}/evaluate-test-chemistry';
      } else if (['mathematics', 'math', 'maths'].contains(subject)) {
        endpoint = '${AppConfig.apiBaseUrl}/evaluate-test-maths';
      } else if (['biology', 'bio'].contains(subject)) {
        endpoint = '${AppConfig.apiBaseUrl}/evaluate-test';
      } else {
        debugPrint('Error: Unknown subject: $subject');
        Fluttertoast.showToast(
          msg: 'Error: Test subject is not defined.',
          backgroundColor: material.Colors.red,
        );
        setState(() => _isSubmitting = false);
        return;
      }

      final response = await http
          .post(
            Uri.parse(endpoint),
            headers: {
              ...AppConfig.defaultHeaders,
              'Content-Type': 'application/json',
            },
            body: json.encode(payload),
          )
          .timeout(AppConfig.apiTimeout);

      debugPrint('API Request: $endpoint');
      debugPrint('Payload: ${json.encode(payload)}');
      debugPrint('Status Code: ${response.statusCode}');
      debugPrint('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        setState(() {
          _feedbackData = FeedbackData.fromJson(responseData);
          _showFeedback = true;
        });
        Fluttertoast.showToast(
          msg: 'Test submitted successfully!',
          backgroundColor: material.Colors.green,
        );
      } else {
        debugPrint('Error: Failed to submit test: ${response.statusCode}');
        Fluttertoast.showToast(
          msg: 'Failed to submit test: ${response.statusCode}',
          backgroundColor: material.Colors.red,
        );
      }
    } catch (error) {
      debugPrint('Error submitting test: $error');
      Fluttertoast.showToast(
        msg: 'Error submitting test: $error',
        backgroundColor: material.Colors.red,
      );
    } finally {
      setState(() => _isSubmitting = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final testData = ref.watch(testProvider);
    debugPrint('testData in build: ${testData?.examId}');

    if (testData == null) {
      debugPrint('testData is null in build');
      return material.Scaffold(
        body: material.Center(
          child: material.Text(
            'No test data found',
            style: material.TextStyle(fontSize: 18, color: material.Colors.red),
          ),
        ),
      );
    }
    if (testData.questions.isEmpty) {
      debugPrint('testData questions empty');
      return material.Scaffold(
        body: material.Center(
          child: material.Text(
            'No questions found',
            style: material.TextStyle(fontSize: 18, color: material.Colors.red),
          ),
        ),
      );
    }

    final q = testData.questions[_currentIndex];
    final selectedOption = _userAnswers[q.id] ?? '';
    final minutes = (_timeLeft / 60).floor();
    final seconds = _timeLeft % 60;

    return material.Scaffold(
      body: material.Stack(
        children: [
          const CosmicBackground(),
          material.SafeArea(
            child: material.SingleChildScrollView(
              child: material.Padding(
                padding: const material.EdgeInsets.symmetric(horizontal: 16, vertical: 20),
                child: material.Column(
                  crossAxisAlignment: material.CrossAxisAlignment.start,
                  children: [
                    // Header Section
                    Animate(
                      effects: [
                        FadeEffect(duration: 300.milliseconds),
                        MoveEffect(
                          begin: const material.Offset(0, -20),
                          end: material.Offset.zero,
                          duration: 300.milliseconds,
                          curve: material.Curves.easeOut,
                        ),
                      ],
                      child: material.Container(
                        padding: const material.EdgeInsets.all(16),
                        decoration: material.BoxDecoration(
                          gradient: const material.LinearGradient(
                            colors: [material.Color(0xFF4F46E5), material.Color(0xFF2563EB)],
                            begin: material.Alignment.centerLeft,
                            end: material.Alignment.centerRight,
                          ),
                          borderRadius: material.BorderRadius.circular(16),
                          boxShadow: [
                            material.BoxShadow(
                              color: material.Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              offset: const material.Offset(0, 4),
                            ),
                          ],
                        ),
                        child: material.Column(
                          crossAxisAlignment: material.CrossAxisAlignment.start,
                          children: [
                            material.Text(
                              testData.examName,
                              style: const material.TextStyle(
                                fontSize: 24,
                                fontWeight: material.FontWeight.bold,
                                color: material.Colors.white,
                              ),
                              overflow: material.TextOverflow.ellipsis,
                            ),
                            const material.SizedBox(height: 8),
                            material.Wrap(
                              spacing: 8,
                              alignment: material.WrapAlignment.spaceBetween,
                              children: [
                                material.Text(
                                  'Question ${_currentIndex + 1} of ${testData.numQuestions}',
                                  style: const material.TextStyle(
                                    fontSize: 16,
                                    color: material.Color(0xFFE0E7FF),
                                  ),
                                ),
                                material.Container(
                                  padding: const material.EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 6,
                                  ),
                                  decoration: material.BoxDecoration(
                                    color: material.Colors.white.withOpacity(0.1),
                                    borderRadius: material.BorderRadius.circular(8),
                                  ),
                                  child: material.Row(
                                    mainAxisSize: material.MainAxisSize.min,
                                    children: [
                                      const FaIcon(
                                        FontAwesomeIcons.clock,
                                        size: 20,
                                        color: material.Colors.white,
                                      ),
                                      const material.SizedBox(width: 8),
                                      material.Text(
                                        '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}',
                                        style: const material.TextStyle(
                                          fontFamily: 'Courier',
                                          fontSize: 16,
                                          color: material.Colors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const material.SizedBox(height: 12),
                            material.Row(
                              mainAxisAlignment: material.MainAxisAlignment.spaceBetween,
                              children: [
                                const material.Text(
                                  'Progress',
                                  style: material.TextStyle(
                                    fontSize: 14,
                                    color: material.Color(0xFFE0E7FF),
                                  ),
                                ),
                                material.Text(
                                  '${((_currentIndex + 1) / testData.numQuestions * 100).round()}%',
                                  style: const material.TextStyle(
                                    fontSize: 14,
                                    color: material.Color(0xFFE0E7FF),
                                  ),
                                ),
                              ],
                            ),
                            material.Container(
                              height: 8,
                              decoration: material.BoxDecoration(
                                color: const material.Color(0xFF312E81),
                                borderRadius: material.BorderRadius.circular(4),
                              ),
                              child: Animate(
                                effects: [
                                  MoveEffect(duration: 500.milliseconds),
                                ],
                                child: material.FractionallySizedBox(
                                  widthFactor: (_currentIndex + 1) / testData.numQuestions,
                                  child: material.Container(
                                    decoration: material.BoxDecoration(
                                      color: material.Colors.white,
                                      borderRadius: material.BorderRadius.circular(4),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const material.SizedBox(height: 24),
                    // Question Section
                    Animate(
                      key: ValueKey(_currentIndex),
                      effects: [
                        FadeEffect(duration: 400.milliseconds),
                        MoveEffect(
                          begin: const material.Offset(0, 20),
                          end: material.Offset.zero,
                          duration: 400.milliseconds,
                          curve: material.Curves.easeOut,
                        ),
                      ],
                      child: material.Container(
                        padding: const material.EdgeInsets.all(16),
                        decoration: material.BoxDecoration(
                          color: material.Colors.white,
                          borderRadius: material.BorderRadius.circular(16),
                          boxShadow: [
                            material.BoxShadow(
                              color: material.Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              offset: const material.Offset(0, 4),
                            ),
                          ],
                        ),
                        child: material.SingleChildScrollView(
                          child: material.Column(
                            crossAxisAlignment: material.CrossAxisAlignment.start,
                            children: [
                              material.Row(
                                crossAxisAlignment: material.CrossAxisAlignment.start,
                                children: [
                                  material.Container(
                                    width: 48,
                                    height: 48,
                                    decoration: material.BoxDecoration(
                                      color: const material.Color(0xFFE0E7FF),
                                      shape: material.BoxShape.circle,
                                    ),
                                    child: material.Center(
                                      child: material.Text(
                                        '${_currentIndex + 1}',
                                        style: const material.TextStyle(
                                          fontSize: 20,
                                          fontWeight: material.FontWeight.bold,
                                          color: material.Color(0xFF4F46E5),
                                        ),
                                      ),
                                    ),
                                  ),
                                  const material.SizedBox(width: 16),
                                  material.Expanded(
                                    child: material.Text(
                                      q.questionText,
                                      style: const material.TextStyle(
                                        fontSize: 18,
                                        fontWeight: material.FontWeight.w600,
                                        color: material.Colors.black87,
                                      ),
                                      overflow: material.TextOverflow.visible,
                                    ),
                                  ),
                                ],
                              ),
                              if (q.imageUrl != null) ...[
                                const material.SizedBox(height: 16),
                                Animate(
                                  effects: [
                                    FadeEffect(
                                      delay: 200.milliseconds,
                                      duration: 300.milliseconds,
                                    ),
                                    ScaleEffect(
                                      delay: 200.milliseconds,
                                      begin: const material.Offset(0.95, 0.95),
                                      end: const material.Offset(1, 1),
                                      duration: 300.milliseconds,
                                    ),
                                  ],
                                  child: material.Image.network(
                                    q.imageUrl!,
                                    height: 150,
                                    fit: material.BoxFit.contain,
                                    errorBuilder: (context, error, stackTrace) =>
                                        const material.Text(
                                      'Failed to load image',
                                      style: material.TextStyle(color: material.Colors.red),
                                    ),
                                  ),
                                ),
                              ],
                              const material.SizedBox(height: 16),
                              _renderOptions(q.options, selectedOption, q.id),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const material.SizedBox(height: 24),
                    // Navigation Buttons
                    material.Wrap(
                      spacing: 16,
                      alignment: material.WrapAlignment.spaceBetween,
                      children: [
                        Animate(
                          child: material.ElevatedButton(
                            onPressed: _currentIndex == 0 ? null : _handlePrevious,
                            style: material.ElevatedButton.styleFrom(
                              backgroundColor: _currentIndex == 0
                                  ? material.Colors.grey.shade100
                                  : const material.Color(0xFFE0E7FF),
                              foregroundColor: _currentIndex == 0
                                  ? material.Colors.grey.shade400
                                  : const material.Color(0xFF4F46E5),
                              padding: const material.EdgeInsets.symmetric(
                                  horizontal: 20, vertical: 12),
                              shape: material.RoundedRectangleBorder(
                                borderRadius: material.BorderRadius.circular(12),
                              ),
                            ),
                            child: const material.Row(
                              mainAxisSize: material.MainAxisSize.min,
                              children: [
                                FaIcon(FontAwesomeIcons.chevronLeft, size: 18),
                                material.SizedBox(width: 8),
                                material.Text(
                                  'Previous',
                                  style: material.TextStyle(
                                    fontSize: 16,
                                    fontWeight: material.FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          effects: [
                            ScaleEffect(
                              begin: const material.Offset(1, 1),
                              end: const material.Offset(1.05, 1.05),
                              duration: 200.milliseconds,
                              curve: material.Curves.easeOut,
                            ),
                          ],
                        ),
                        Animate(
                          child: material.ElevatedButton(
                            onPressed: _currentIndex == testData.numQuestions - 1
                                ? _handleSubmitTest
                                : _handleNext,
                            style: material.ElevatedButton.styleFrom(
                              backgroundColor: _currentIndex == testData.numQuestions - 1
                                  ? const material.Color(0xFF16A34A)
                                  : const material.Color(0xFF4F46E5),
                              foregroundColor: material.Colors.white,
                              padding: const material.EdgeInsets.symmetric(
                                  horizontal: 20, vertical: 12),
                              shape: material.RoundedRectangleBorder(
                                borderRadius: material.BorderRadius.circular(12),
                              ),
                            ),
                            child: material.Row(
                              mainAxisSize: material.MainAxisSize.min,
                              children: [
                                material.Text(
                                  _currentIndex == testData.numQuestions - 1
                                      ? 'Submit Test'
                                      : 'Next',
                                  style: const material.TextStyle(
                                    fontSize: 16,
                                    fontWeight: material.FontWeight.w600,
                                  ),
                                ),
                                const material.SizedBox(width: 8),
                                FaIcon(
                                  _currentIndex == testData.numQuestions - 1
                                      ? FontAwesomeIcons.check
                                      : FontAwesomeIcons.chevronRight,
                                  size: 18,
                                ),
                              ],
                            ),
                          ),
                          effects: [
                            ScaleEffect(
                              begin: const material.Offset(1, 1),
                              end: const material.Offset(1.05, 1.05),
                              duration: 200.milliseconds,
                              curve: material.Curves.easeOut,
                            ),
                          ],
                        ),
                      ],
                    ),
                    const material.SizedBox(height: 24),
                    // Question Navigation
                    material.Container(
                      padding: const material.EdgeInsets.all(16),
                      decoration: material.BoxDecoration(
                        color: material.Colors.white,
                        borderRadius: material.BorderRadius.circular(16),
                        boxShadow: [
                          material.BoxShadow(
                            color: material.Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const material.Offset(0, 4),
                          ),
                        ],
                      ),
                      child: material.Column(
                        crossAxisAlignment: material.CrossAxisAlignment.start,
                        children: [
                          const material.Text(
                            'Question Navigation',
                            style: material.TextStyle(
                              fontSize: 20,
                              fontWeight: material.FontWeight.w600,
                              color: material.Colors.black87,
                            ),
                          ),
                          const material.SizedBox(height: 16),
                          material.Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: List.generate(testData.numQuestions, (index) {
                              final isAnswered =
                                  _userAnswers.containsKey(testData.questions[index].id);
                              final isCurrent = index == _currentIndex;
                              return Animate(
                                effects: [
                                  ScaleEffect(
                                    begin: const material.Offset(1, 1),
                                    end: const material.Offset(1.1, 1.1),
                                    duration: 200.milliseconds,
                                    curve: material.Curves.easeOut,
                                  ),
                                ],
                                child: material.ElevatedButton(
                                  onPressed: () => setState(() => _currentIndex = index),
                                  style: material.ElevatedButton.styleFrom(
                                    backgroundColor: isCurrent
                                        ? const material.Color(0xFF4F46E5)
                                        : isAnswered
                                            ? const material.Color(0xFFD1FAE5)
                                            : material.Colors.grey.shade100,
                                    foregroundColor: isCurrent
                                        ? material.Colors.white
                                        : isAnswered
                                            ? const material.Color(0xFF15803D)
                                            : material.Colors.grey.shade700,
                                    shape: const material.CircleBorder(),
                                    padding: const material.EdgeInsets.all(12),
                                  ),
                                  child: material.Text(
                                    '${index + 1}',
                                    style: material.TextStyle(
                                      fontSize: 16,
                                      fontWeight: material.FontWeight.w600,
                                      color: isCurrent
                                          ? material.Colors.white
                                          : isAnswered
                                              ? const material.Color(0xFF15803D)
                                              : material.Colors.grey.shade700,
                                    ),
                                  ),
                                ),
                              );
                            }),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          if (_isSubmitting)
            material.Container(
              color: material.Colors.black.withOpacity(0.5),
              child: material.Center(
                child: material.Container(
                  padding: const material.EdgeInsets.all(24),
                  decoration: material.BoxDecoration(
                    color: material.Colors.white.withOpacity(0.1),
                    borderRadius: material.BorderRadius.circular(24),
                    border: material.Border.all(color: material.Colors.blue.shade200.withOpacity(0.3)),
                    boxShadow: [
                      material.BoxShadow(
                        color: const material.Color(0xFF2563EB).withOpacity(0.2),
                        blurRadius: 40,
                        offset: const material.Offset(0, 8),
                      ),
                    ],
                  ),
                  child: material.Column(
                    mainAxisSize: material.MainAxisSize.min,
                    children: [
                      material.Stack(
                        alignment: material.Alignment.center,
                        children: [
                          material.Container(
                            width: 100,
                            height: 100,
                            decoration: material.BoxDecoration(
                              shape: material.BoxShape.circle,
                              color: const material.Color(0xFF2563EB).withOpacity(0.15),
                            ),
                          ),
                          Animate(
                            effects: [
                              RotateEffect(
                                duration: const Duration(seconds: 8),
                                curve: material.Curves.linear,
                              ),
                            ],
                            child: CustomPaint(
                              size: const material.Size(100, 100),
                              painter: OrbitPainter(),
                            ),
                          ),
                        ],
                      ),
                      const material.SizedBox(height: 24),
                      const material.Text(
                        'Feedback Generating',
                        style: material.TextStyle(
                          fontSize: 24,
                          fontWeight: material.FontWeight.bold,
                          color: material.Colors.white,
                        ),
                      ),
                      const material.SizedBox(height: 8),
                      const material.Text(
                        'Evaluating your answers with generating Feedback. Please wait...',
                        style: material.TextStyle(
                          fontSize: 16,
                          color: material.Color(0xFFBFDBFE),
                        ),
                        textAlign: material.TextAlign.center,
                        maxLines: 2,
                      ),
                      const material.SizedBox(height: 24),
                      material.Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        alignment: material.WrapAlignment.center,
                        children: [
                          _buildMetricCard('Accuracy', '✓', const material.Color(0xFF16A34A)),
                          _buildMetricCard('Speed', '⚡', const material.Color(0xFFFACC15)),
                          _buildMetricCard('Depth', '🔍', const material.Color(0xFFBFDBFE)),
                        ],
                      ),
                    ],
                  ),
                ).animate(
                  effects: [
                    ScaleEffect(
                      begin: const material.Offset(0.95, 0.95),
                      end: const material.Offset(1, 1),
                      duration: 300.milliseconds,
                      curve: material.Curves.easeOut,
                    ),
                    MoveEffect(
                      begin: const material.Offset(0, 30),
                      end: material.Offset.zero,
                      duration: 300.milliseconds,
                      curve: material.Curves.easeOut,
                    ),
                  ],
                ),
              ),
            ),
          if (_showFeedback && _feedbackData != null)
            material.Dialog(
              backgroundColor: material.Colors.transparent,
              child: material.Container(
                constraints: const material.BoxConstraints(maxWidth: 600, maxHeight: 800),
                decoration: material.BoxDecoration(
                  gradient: const material.LinearGradient(
                    colors: [material.Colors.white, material.Color(0xFFF9FAFB)],
                    begin: material.Alignment.topLeft,
                    end: material.Alignment.bottomRight,
                  ),
                  borderRadius: material.BorderRadius.circular(16),
                  boxShadow: [
                    material.BoxShadow(
                      color: material.Colors.black.withOpacity(0.15),
                      blurRadius: 20,
                      offset: const material.Offset(0, 10),
                    ),
                  ],
                ),
                child: material.SingleChildScrollView(
                  child: material.Column(
                    mainAxisSize: material.MainAxisSize.min,
                    children: [
                      // Feedback Header
                      material.Container(
                        padding: const material.EdgeInsets.all(16),
                        decoration: const material.BoxDecoration(
                          gradient: material.LinearGradient(
                            colors: [material.Color(0xFF4F46E5), material.Color(0xFF2563EB)],
                            begin: material.Alignment.centerLeft,
                            end: material.Alignment.centerRight,
                          ),
                          borderRadius: material.BorderRadius.vertical(
                            top: material.Radius.circular(16),
                          ),
                        ),
                        child: material.Row(
                          children: [
                            material.Container(
                              padding: const material.EdgeInsets.all(8),
                              decoration: material.BoxDecoration(
                                color: material.Colors.white.withOpacity(0.2),
                                borderRadius: material.BorderRadius.circular(8),
                              ),
                              child: const FaIcon(
                                FontAwesomeIcons.fileLines,
                                size: 24,
                                color: material.Colors.white,
                              ),
                            ),
                            const material.SizedBox(width: 12),
                            material.Expanded(
                              child: material.Text(
                                '${testData.examName} Results',
                                style: const material.TextStyle(
                                  fontSize: 20,
                                  fontWeight: material.FontWeight.bold,
                                  color: material.Colors.white,
                                ),
                                overflow: material.TextOverflow.ellipsis,
                              ),
                            ),
                            material.IconButton(
                              icon: const FaIcon(
                                FontAwesomeIcons.xmark,
                                color: material.Colors.white,
                              ),
                              onPressed: () {
                                setState(() {
                                  _showFeedback = false;
                                  _feedbackData = null;
                                });
                                material.Navigator.pop(context);
                              },
                            ),
                          ],
                        ),
                      ),
                      // Score Cards
                      material.Padding(
                        padding: const material.EdgeInsets.all(16),
                        child: material.Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          alignment: material.WrapAlignment.center,
                          children: [
                            _buildScoreCard(
                              'Correct',
                              _feedbackData!.scoreSummary['num_correct'].toString(),
                              FontAwesomeIcons.checkCircle,
                              const material.Color(0xFF16A34A),
                            ),
                            _buildScoreCard(
                              'Incorrect',
                              _feedbackData!.scoreSummary['num_incorrect'].toString(),
                              FontAwesomeIcons.xmarkCircle,
                              const material.Color(0xFFDC2626),
                            ),
                            _buildScoreCard(
                              'Unattempted',
                              _feedbackData!.scoreSummary['num_unattempted'].toString(),
                              FontAwesomeIcons.minusCircle,
                              const material.Color(0xFFF59E0B),
                            ),
                          ],
                        ),
                      ),
                      // Progress Bar
                      material.Padding(
                        padding: const material.EdgeInsets.symmetric(horizontal: 16),
                        child: material.Container(
                          height: 8,
                          decoration: material.BoxDecoration(
                            color: material.Colors.grey.shade200,
                            borderRadius: material.BorderRadius.circular(4),
                          ),
                          child: Animate(
                            effects: [
                              MoveEffect(
                                duration: 500.milliseconds,
                                curve: material.Curves.easeOut,
                              ),
                            ],
                            child: material.FractionallySizedBox(
                              widthFactor: _feedbackData!.scoreSummary['score'] /
                                  _feedbackData!.scoreSummary['total_questions'],
                              child: material.Container(
                                decoration: material.BoxDecoration(
                                  color: const material.Color(0xFF16A34A),
                                  borderRadius: material.BorderRadius.circular(4),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      // Performance Overview
                      material.Padding(
                        padding: const material.EdgeInsets.all(16),
                        child: _buildSection(
                          'Performance Overview',
                          FontAwesomeIcons.star,
                          const material.Color(0xFF4F46E5),
                          material.Column(
                            children: [
                              material.Container(
                                padding: const material.EdgeInsets.all(12),
                                decoration: material.BoxDecoration(
                                  color: const material.Color(0xFFE0E7FF).withOpacity(0.5),
                                  borderRadius: material.BorderRadius.circular(12),
                                ),
                                child: material.Text(
                                  _feedbackData!.overallAssessment,
                                  style: const material.TextStyle(
                                    fontSize: 14,
                                    color: material.Colors.black87,
                                  ),
                                  overflow: material.TextOverflow.visible,
                                ),
                              ),
                              const material.SizedBox(height: 12),
                              material.Text(
                                _feedbackData!.motivationalClosing,
                                style: const material.TextStyle(
                                  fontSize: 14,
                                  fontStyle: material.FontStyle.italic,
                                  color: material.Color(0xFF4F46E5),
                                  fontWeight: material.FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          delay: 400.milliseconds,
                        ),
                      ),
                      // Question Analysis
                      material.Padding(
                        padding: const material.EdgeInsets.all(16),
                        child: _buildSection(
                          'Question Analysis',
                          FontAwesomeIcons.list,
                          const material.Color(0xFF2563EB),
                          material.Column(
                            children: [
                              ..._feedbackData!.detailedQuestionFeedback.asMap().entries.map((entry) {
                                final i = entry.key;
                                final feedback = entry.value;
                                return Animate(
                                  effects: [
                                    FadeEffect(
                                      delay: (100 * i).milliseconds,
                                      duration: 300.milliseconds,
                                    ),
                                  ],
                                  child: material.Container(
                                    margin: const material.EdgeInsets.only(bottom: 12),
                                    padding: const material.EdgeInsets.all(12),
                                    decoration: material.BoxDecoration(
                                      borderRadius: material.BorderRadius.circular(12),
                                      border: material.Border.all(
                                        color: feedback['validation']
                                            ? const material.Color(0xFFD1FAE5)
                                            : feedback['user_answer'] != null
                                                ? const material.Color(0xFFFEE2E2)
                                                : const material.Color(0xFFFFF3CD),
                                      ),
                                      color: feedback['validation']
                                          ? const material.Color(0xFFD1FAE5).withOpacity(0.5)
                                          : feedback['user_answer'] != null
                                              ? const material.Color(0xFFFEE2E2).withOpacity(0.5)
                                              : const material.Color(0xFFFFF3CD).withOpacity(0.5),
                                    ),
                                    child: material.Column(
                                      crossAxisAlignment: material.CrossAxisAlignment.start,
                                      children: [
                                        material.Row(
                                          mainAxisAlignment: material.MainAxisAlignment.spaceBetween,
                                          children: [
                                            material.Text(
                                              'Question ${i + 1}',
                                              style: const material.TextStyle(
                                                fontSize: 16,
                                                fontWeight: material.FontWeight.w600,
                                                color: material.Colors.black87,
                                              ),
                                            ),
                                            material.Container(
                                              padding: const material.EdgeInsets.symmetric(
                                                horizontal: 8,
                                                vertical: 4,
                                              ),
                                              decoration: material.BoxDecoration(
                                                color: feedback['validation']
                                                    ? const material.Color(0xFFD1FAE5)
                                                    : feedback['user_answer'] != null
                                                        ? const material.Color(0xFFFEE2E2)
                                                        : const material.Color(0xFFFFF3CD),
                                                borderRadius: material.BorderRadius.circular(8),
                                              ),
                                              child: material.Text(
                                                feedback['validation']
                                                    ? 'Correct'
                                                    : feedback['user_answer'] != null
                                                        ? 'Incorrect'
                                                        : 'Unattempted',
                                                style: material.TextStyle(
                                                  fontSize: 12,
                                                  color: feedback['validation']
                                                      ? const material.Color(0xFF16A34A)
                                                      : feedback['user_answer'] != null
                                                          ? const material.Color(0xFFDC2626)
                                                          : const material.Color(0xFFF59E0B),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        const material.SizedBox(height: 8),
                                        material.Text(
                                          feedback['feedback_on_answer'],
                                          style: const material.TextStyle(
                                            fontSize: 14,
                                            color: material.Colors.black54,
                                          ),
                                          overflow: material.TextOverflow.visible,
                                        ),
                                        const material.SizedBox(height: 12),
                                        material.Wrap(
                                          spacing: 12,
                                          runSpacing: 12,
                                          children: [
                                            material.Expanded(
                                              child: material.Container(
                                                padding: const material.EdgeInsets.all(8),
                                                decoration: material.BoxDecoration(
                                                  color: material.Colors.white,
                                                  borderRadius: material.BorderRadius.circular(8),
                                                  border: material.Border.all(
                                                      color: material.Colors.grey.shade200),
                                                ),
                                                child: material.Column(
                                                  crossAxisAlignment: material.CrossAxisAlignment.start,
                                                  children: [
                                                    const material.Text(
                                                      'Your Answer',
                                                      style: material.TextStyle(
                                                        fontSize: 14,
                                                        fontWeight: material.FontWeight.w500,
                                                        color: material.Colors.black54,
                                                      ),
                                                    ),
                                                    material.Text(
                                                      feedback['user_answer'] ?? 'Not attempted',
                                                      style: material.TextStyle(
                                                        fontSize: 14,
                                                        fontWeight: material.FontWeight.w500,
                                                        color: feedback['validation']
                                                            ? const material.Color(0xFF16A34A)
                                                            : const material.Color(0xFFDC2626),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            material.Expanded(
                                              child: material.Container(
                                                padding: const material.EdgeInsets.all(8),
                                                decoration: material.BoxDecoration(
                                                  color: material.Colors.white,
                                                  borderRadius: material.BorderRadius.circular(8),
                                                  border: material.Border.all(
                                                      color: material.Colors.grey.shade200),
                                                ),
                                                child: material.Column(
                                                  crossAxisAlignment: material.CrossAxisAlignment.start,
                                                  children: [
                                                    const material.Text(
                                                      'Correct Answer',
                                                      style: material.TextStyle(
                                                        fontSize: 14,
                                                        fontWeight: material.FontWeight.w500,
                                                        color: material.Colors.black54,
                                                      ),
                                                    ),
                                                    material.Text(
                                                      feedback['correct_answer'],
                                                      style: const material.TextStyle(
                                                        fontSize: 14,
                                                        fontWeight: material.FontWeight.w500,
                                                        color: material.Color(0xFF16A34A),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        if (feedback['reinforce_concept_from_solution'] != null) ...[
                                          const material.SizedBox(height: 12),
                                          material.Container(
                                            padding: const material.EdgeInsets.all(12),
                                            decoration: material.BoxDecoration(
                                              color: const material.Color(0xFFDBEAFE),
                                              borderRadius: material.BorderRadius.circular(8),
                                            ),
                                            child: material.Row(
                                              children: [
                                                const FaIcon(
                                                  FontAwesomeIcons.lightbulb,
                                                  size: 16,
                                                  color: material.Color(0xFF2563EB),
                                                ),
                                                const material.SizedBox(width: 8),
                                                material.Expanded(
                                                  child: material.Text(
                                                    feedback['reinforce_concept_from_solution'],
                                                    style: const material.TextStyle(
                                                      fontSize: 14,
                                                      color: material.Color(0xFF2563EB),
                                                    ),
                                                    overflow: material.TextOverflow.visible,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                );
                              }),
                            ],
                          ),
                          delay: 500.milliseconds,
                        ),
                      ),
                      // Strengths and Improvements
                      material.Padding(
                        padding: const material.EdgeInsets.all(16),
                        child: material.Wrap(
                          spacing: 16,
                          runSpacing: 16,
                          children: [
                            material.Expanded(
                              child: _buildSection(
                                'Your Strengths',
                                FontAwesomeIcons.award,
                                const material.Color(0xFF16A34A),
                                material.Column(
                                  children: _feedbackData!.topicStrengths.asMap().entries.map((entry) {
                                    final i = entry.key;
                                    final strength = entry.value;
                                    return Animate(
                                      effects: [
                                        FadeEffect(
                                          delay: (100 * i).milliseconds,
                                          duration: 300.milliseconds,
                                        ),
                                      ],
                                      child: material.Container(
                                        margin: const material.EdgeInsets.only(bottom: 8),
                                        padding: const material.EdgeInsets.all(12),
                                        decoration: material.BoxDecoration(
                                          color: const material.Color(0xFFD1FAE5).withOpacity(0.5),
                                          borderRadius: material.BorderRadius.circular(8),
                                        ),
                                        child: material.Row(
                                          children: [
                                            const FaIcon(
                                              FontAwesomeIcons.check,
                                              size: 16,
                                              color: material.Color(0xFF16A34A),
                                            ),
                                            const material.SizedBox(width: 12),
                                            material.Expanded(
                                              child: material.Text(
                                                strength,
                                                style: const material.TextStyle(
                                                  fontSize: 14,
                                                  color: material.Colors.black87,
                                                ),
                                                overflow: material.TextOverflow.visible,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ),
                                delay: 600.milliseconds,
                              ),
                            ),
                            material.Expanded(
                              child: _buildSection(
                                'Areas to Improve',
                                FontAwesomeIcons.chartLine,
                                const material.Color(0xFFF59E0B),
                                material.Column(
                                  children: _feedbackData!.aiFeedback['areas_for_improvement']
                                          ?.asMap()
                                          .entries
                                          .map<Widget>((entry) {
                                        final i = entry.key;
                                        final item = entry.value;
                                        return Animate(
                                          effects: [
                                            FadeEffect(
                                              delay: (100 * i).milliseconds,
                                              duration: 300.milliseconds,
                                            ),
                                          ],
                                          child: material.Container(
                                            margin: const material.EdgeInsets.only(bottom: 8),
                                            padding: const material.EdgeInsets.all(12),
                                            decoration: material.BoxDecoration(
                                              color: const material.Color(0xFFFFF3CD).withOpacity(0.5),
                                              borderRadius: material.BorderRadius.circular(8),
                                            ),
                                            child: material.Row(
                                              children: [
                                                const FaIcon(
                                                  FontAwesomeIcons.triangleExclamation,
                                                  size: 16,
                                                  color: material.Color(0xFFF59E0B),
                                                ),
                                                const material.SizedBox(width: 12),
                                                material.Expanded(
                                                  child: material.Text(
                                                    item,
                                                    style: const material.TextStyle(
                                                      fontSize: 14,
                                                      color: material.Colors.black87,
                                                    ),
                                                    overflow: material.TextOverflow.visible,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        );
                                      }).toList() ??
                                      [],
                                ),
                                delay: 700.milliseconds,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Study Tips
                      material.Padding(
                        padding: const material.EdgeInsets.all(16),
                        child: _buildSection(
                          'Study Tips',
                          FontAwesomeIcons.lightbulb,
                          const material.Color(0xFF2563EB),
                          material.Wrap(
                            spacing: 12,
                            runSpacing: 12,
                            children: _feedbackData!.generalStudyTips.asMap().entries.map((entry) {
                              final i = entry.key;
                              final tip = entry.value;
                              return Animate(
                                effects: [
                                  FadeEffect(
                                    delay: (100 * i).milliseconds,
                                    duration: 300.milliseconds,
                                  ),
                                ],
                                child: material.Container(
                                  width: double.infinity,
                                  padding: const material.EdgeInsets.all(12),
                                  decoration: material.BoxDecoration(
                                    color: const material.Color(0xFFDBEAFE).withOpacity(0.5),
                                    borderRadius: material.BorderRadius.circular(8),
                                  ),
                                  child: material.Row(
                                    children: [
                                      const FaIcon(
                                        FontAwesomeIcons.circle,
                                        size: 16,
                                        color: material.Color(0xFF60A5FA),
                                      ),
                                      const material.SizedBox(width: 8),
                                      material.Expanded(
                                        child: material.Text(
                                          tip,
                                          style: const material.TextStyle(
                                            fontSize: 14,
                                            color: material.Colors.black87,
                                          ),
                                          overflow: material.TextOverflow.visible,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                          delay: 800.milliseconds,
                        ),
                      ),
                      // Close Button
                      material.Padding(
                        padding: const material.EdgeInsets.all(16),
                        child: material.ElevatedButton(
                          onPressed: () {
                            setState(() {
                              _showFeedback = false;
                              _feedbackData = null;
                            });
                            material.Navigator.pop(context);
                          },
                          style: material.ElevatedButton.styleFrom(
                            backgroundColor: const material.Color(0xFF4F46E5),
                            foregroundColor: material.Colors.white,
                            padding: const material.EdgeInsets.symmetric(
                                horizontal: 24, vertical: 12),
                            shape: material.RoundedRectangleBorder(
                              borderRadius: material.BorderRadius.circular(12),
                            ),
                          ),
                          child: const material.Text(
                            'Close',
                            style: material.TextStyle(
                              fontSize: 16,
                              fontWeight: material.FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildScoreCard(String title, String value, IconData icon, material.Color color) {
    return material.Expanded(
      child: material.Container(
        padding: const material.EdgeInsets.all(12),
        decoration: material.BoxDecoration(
          color: material.Colors.white.withOpacity(0.1),
          borderRadius: material.BorderRadius.circular(12),
        ),
        child: material.Column(
          children: [
            material.Row(
              mainAxisAlignment: material.MainAxisAlignment.center,
              children: [
                FaIcon(icon, size: 18, color: color),
                const material.SizedBox(width: 8),
                material.Text(
                  title,
                  style: const material.TextStyle(
                    fontSize: 14,
                    fontWeight: material.FontWeight.w500,
                    color: material.Colors.white,
                  ),
                  overflow: material.TextOverflow.ellipsis,
                ),
              ],
            ),
            const material.SizedBox(height: 8),
            material.Text(
              value,
              style: const material.TextStyle(
                fontSize: 24,
                fontWeight: material.FontWeight.bold,
                color: material.Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, IconData icon, material.Color iconColor, Widget content,
      {required Duration delay}) {
    return material.Column(
      crossAxisAlignment: material.CrossAxisAlignment.start,
      children: [
        material.Row(
          children: [
            material.Container(
              padding: const material.EdgeInsets.all(8),
              decoration: material.BoxDecoration(
                color: iconColor.withOpacity(0.1),
                borderRadius: material.BorderRadius.circular(8),
              ),
              child: FaIcon(
                icon,
                size: 20,
                color: iconColor,
              ),
            ),
            const material.SizedBox(width: 12),
            material.Text(
              title,
              style: const material.TextStyle(
                fontSize: 18,
                fontWeight: material.FontWeight.w600,
                color: material.Colors.black87,
              ),
            ),
          ],
        ),
        const material.SizedBox(height: 12),
        content,
      ],
    );
  }
}

class OrbitPainter extends CustomPainter {
  @override
  void paint(material.Canvas canvas, material.Size size) {
    final center = material.Offset(size.width / 2, size.height / 2);
    final paint = material.Paint()
      ..style = material.PaintingStyle.stroke
      ..strokeWidth = 2;

    paint.color = const material.Color(0xFF2563EB).withOpacity(0.5);
    canvas.drawOval(
      material.Rect.fromCenter(center: center, width: 64, height: 36),
      paint,
    );

    paint.color = material.Colors.white.withOpacity(0.3);
    paint.strokeWidth = 1.5;
    canvas.drawOval(
      material.Rect.fromCenter(center: center, width: 36, height: 64),
      paint,
    );

    paint.style = material.PaintingStyle.fill;
    paint.color = material.Colors.white;
    canvas.drawCircle(
      material.Offset(size.width / 2 + 32, size.height / 2),
      4,
      paint,
    );
    paint.color = const material.Color(0xFF2563EB);
    canvas.drawCircle(
      material.Offset(size.width / 2, size.height / 2 - 32),
      3,
      paint,
    );
    paint.color = material.Colors.white;
    canvas.drawCircle(
      material.Offset(size.width / 2 - 32, size.height / 2),
      2.5,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WavePainter extends CustomPainter {
  @override
  void paint(material.Canvas canvas, material.Size size) {
    final paint = material.Paint()
      ..color = material.Colors.white.withOpacity(0.5)
      ..style = material.PaintingStyle.stroke
      ..strokeWidth = 2;

    final path = material.Path();
    path.moveTo(0, size.height / 2);
    for (double x = 0; x <= size.width; x += 16) {
      final y = size.height / 2 +
          (x % 32 < 16 ? -4 : 4) * (x % 16 < 8 ? 1 : -1);
      path.lineTo(x, y);
    }
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}