/// Represents a single YouTube search result item.
/// NOTE: Adjust fields based on your actual API response.
class YoutubeSearchResult {
  final String title;
  final String videoId;
  final String thumbnailUrl;
  final String channelTitle;

  YoutubeSearchResult({
    required this.title,
    required this.videoId,
    required this.thumbnailUrl,
    required this.channelTitle,
  });

  factory YoutubeSearchResult.fromJson(Map<String, dynamic> json) {
    final String url = json['url'] ?? '';
    String videoId = '';

    // Extract the video ID from the full YouTube URL
    if (url.isNotEmpty) {
      try {
        // A robust way to get the 'v' query parameter
        videoId = Uri.parse(url).queryParameters['v'] ?? '';
      } catch (_) {
        // Fallback in case of a malformed URL
        videoId = '';
      }
    }
    return YoutubeSearchResult(
      title: json['title'] ?? 'No Title',
      videoId: videoId,
      thumbnailUrl: videoId.isNotEmpty
          ? 'https://img.youtube.com/vi/$videoId/mqdefault.jpg'
          : '',
      channelTitle: json['channel'] ?? 'Unknown Channel',
    );
  }
}

/// Represents a single web search result item.
/// NOTE: Adjust fields based on your actual API response.
class WebSearchResult {
  final String title;
  final String link;
  final String snippet;

  WebSearchResult({
    required this.title,
    required this.link,
    required this.snippet,
  });

  factory WebSearchResult.fromJson(Map<String, dynamic> json) {
    return WebSearchResult(
      title: json['title'] ?? 'No Title',
      link: json['link'] ?? '',
      snippet: json['snippet'] ?? 'No snippet available.',
    );
  }
}