import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/config/app_config.dart';
import '../model/onboardingass_model.dart';

class AssessmentService {
  final Dio dio;

  AssessmentService(this.dio);

  Future<String> _getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    final userDataJson = prefs.getString('user_data');
    if (userDataJson == null || userDataJson.isEmpty) {
      throw Exception('User data not found in SharedPreferences');
    }
    final userMap = jsonDecode(userDataJson) as Map<String, dynamic>;
    final userId = userMap['id'] as String?;
    if (userId == null || userId.isEmpty) {
      throw Exception('User ID not found in SharedPreferences');
    }
    return userId;
  }

  Future<AssessmentData> startAssessment() async {
    final userId = await _getUserId();
    final response = await dio.post(
      AppConfig.startAssessmentEndpoint,
      data: {'user_id': userId},
    );
    return AssessmentData.fromJson(response.data);
  }

  Future<void> submitAssessment(Map<String, dynamic> payload) async {
    await dio.post(AppConfig.submitAssessmentEndpoint, data: payload);
  }

  Future<bool> checkAssessmentStatus() async {
    final prefs = await SharedPreferences.getInstance();
    final studentId = prefs.getString('userId');
    final response = await dio.post(
      AppConfig.checkStudentAssessmentEndpoint,
      data: {'student_id': studentId},
    );
    return response.data['assessment_completed'] as bool;
  }

  Future<void> completeAssessment() async {
    final prefs = await SharedPreferences.getInstance();
    final studentId = prefs.getString('userId');
    await dio.post(
      AppConfig.completeStudentAssessmentEndpoint,
      data: {'student_id': studentId},
    );
  }

  Future<Map<String, dynamic>> fetchOnboardData() async {
    final prefs = await SharedPreferences.getInstance();
    final userId = prefs.getString('userId');
    final response = await dio.post('/get-onboard-assessment', data: {'userId': userId});
    return response.data as Map<String, dynamic>;
  }
}
