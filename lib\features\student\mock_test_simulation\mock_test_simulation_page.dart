import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/widgets/base_page.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/logger.dart';

class MockTestSimulationPage extends StatefulWidget {
  const MockTestSimulationPage({super.key});

  @override
  State<MockTestSimulationPage> createState() => _MockTestSimulationPageState();
}

class _MockTestSimulationPageState extends State<MockTestSimulationPage> {
  bool _isLoading = false;
  List<Map<String, dynamic>> _availableTests = [];
  List<Map<String, dynamic>> _recentTests = [];

  @override
  void initState() {
    super.initState();
    _loadTests();
  }

  Future<void> _loadTests() async {
    setState(() => _isLoading = true);
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));
    
    setState(() {
      _availableTests = [
        {
          'id': '1',
          'title': 'JEE Main Mock Test #15',
          'type': 'JEE Main',
          'duration': 180,
          'questions': 90,
          'subjects': ['Physics', 'Chemistry', 'Mathematics'],
          'difficulty': 'Medium',
          'maxMarks': 300,
          'attempts': 0,
          'isLocked': false,
        },
        {
          'id': '2',
          'title': 'NEET Mock Test #12',
          'type': 'NEET',
          'duration': 180,
          'questions': 180,
          'subjects': ['Physics', 'Chemistry', 'Biology'],
          'difficulty': 'Hard',
          'maxMarks': 720,
          'attempts': 0,
          'isLocked': false,
        },
        {
          'id': '3',
          'title': 'JEE Advanced Practice #8',
          'type': 'JEE Advanced',
          'duration': 180,
          'questions': 54,
          'subjects': ['Physics', 'Chemistry', 'Mathematics'],
          'difficulty': 'Hard',
          'maxMarks': 372,
          'attempts': 0,
          'isLocked': true,
        },
      ];
      
      _recentTests = [
        {
          'id': '4',
          'title': 'JEE Main Mock Test #14',
          'type': 'JEE Main',
          'score': 245,
          'maxMarks': 300,
          'percentage': 81.67,
          'rank': 1234,
          'completedAt': DateTime.now().subtract(const Duration(days: 1)),
          'subjects': ['Physics', 'Chemistry', 'Mathematics'],
        },
        {
          'id': '5',
          'title': 'NEET Mock Test #11',
          'type': 'NEET',
          'score': 580,
          'maxMarks': 720,
          'percentage': 80.56,
          'rank': 2156,
          'completedAt': DateTime.now().subtract(const Duration(days: 3)),
          'subjects': ['Physics', 'Chemistry', 'Biology'],
        },
      ];
      
      _isLoading = false;
    });
    
    AppLogger.userAction('Mock tests loaded');
  }

  @override
  Widget build(BuildContext context) {
    return BasePage(
      title: 'Mock Test Simulation',
      subtitle: 'Practice with real exam conditions',
      breadcrumbs: const ['Dashboard', 'Student', 'Mock Test Simulation'],
      isLoading: _isLoading,
      actions: [
        IconButton(
          onPressed: _loadTests,
          icon: const Icon(Icons.refresh),
          tooltip: 'Refresh',
        ),
      ],
      child: _buildTestContent(),
    );
  }

  Widget _buildTestContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatsOverview(),
          const SizedBox(height: 24),
          _buildAvailableTests(),
          const SizedBox(height: 24),
          _buildRecentTests(),
        ],
      ),
    );
  }

  Widget _buildStatsOverview() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF667eea), Color(0xFF764ba2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your Performance',
            style: AppTheme.headingMedium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(child: _buildStatCard('Tests Taken', '12', Icons.quiz)),
              const SizedBox(width: 12),
              Expanded(child: _buildStatCard('Avg Score', '78%', Icons.trending_up)),
              const SizedBox(width: 12),
              Expanded(child: _buildStatCard('Best Rank', '#456', Icons.emoji_events)),
            ],
          ),
        ],
      ),
    ).animate().fadeIn().scale(begin: const Offset(0.9, 0.9));
  }

  Widget _buildStatCard(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.white, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: AppTheme.bodyLarge.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppTheme.bodySmall.copyWith(
              color: Colors.white.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAvailableTests() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Available Tests',
          style: AppTheme.headingSmall.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        if (_availableTests.isEmpty)
          _buildEmptyState('No tests available', 'New tests will appear here')
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _availableTests.length,
            itemBuilder: (context, index) {
              final test = _availableTests[index];
              return _buildTestCard(test, index, true);
            },
          ),
      ],
    );
  }

  Widget _buildRecentTests() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Tests',
          style: AppTheme.headingSmall.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        if (_recentTests.isEmpty)
          _buildEmptyState('No recent tests', 'Your completed tests will appear here')
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _recentTests.length,
            itemBuilder: (context, index) {
              final test = _recentTests[index];
              return _buildTestCard(test, index, false);
            },
          ),
      ],
    );
  }

  Widget _buildTestCard(Map<String, dynamic> test, int index, bool isAvailable) {
    final isLocked = test['isLocked'] ?? false;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: _getTestTypeColor(test['type']).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            isLocked ? Icons.lock : (isAvailable ? Icons.quiz : Icons.assessment),
            color: isLocked ? Colors.grey : _getTestTypeColor(test['type']),
            size: 24,
          ),
        ),
        title: Text(
          test['title'],
          style: AppTheme.bodyLarge.copyWith(
            color: isLocked ? AppTheme.textTertiary : AppTheme.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            if (isAvailable) ...[
              Row(
                children: [
                  const Icon(Icons.access_time, size: 14, color: AppTheme.textSecondary),
                  const SizedBox(width: 4),
                  Text(
                    '${test['duration']} min',
                    style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
                  ),
                  const SizedBox(width: 16),
                  const Icon(Icons.quiz, size: 14, color: AppTheme.textSecondary),
                  const SizedBox(width: 4),
                  Text(
                    '${test['questions']} questions',
                    style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Wrap(
                spacing: 4,
                children: (test['subjects'] as List<String>).map((subject) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: _getSubjectColor(subject).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      subject,
                      style: AppTheme.bodySmall.copyWith(
                        color: _getSubjectColor(subject),
                        fontSize: 10,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ] else ...[
              Row(
                children: [
                  const Icon(Icons.score, size: 14, color: AppTheme.textSecondary),
                  const SizedBox(width: 4),
                  Text(
                    '${test['score']}/${test['maxMarks']} (${test['percentage'].toStringAsFixed(1)}%)',
                    style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
                  ),
                  const SizedBox(width: 16),
                  const Icon(Icons.emoji_events, size: 14, color: AppTheme.textSecondary),
                  const SizedBox(width: 4),
                  Text(
                    'Rank #${test['rank']}',
                    style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
                  ),
                ],
              ),
            ],
          ],
        ),
        trailing: isAvailable
            ? ElevatedButton(
                onPressed: isLocked ? null : () => _startTest(test),
                style: ElevatedButton.styleFrom(
                  backgroundColor: isLocked ? Colors.grey : _getTestTypeColor(test['type']),
                  foregroundColor: Colors.white,
                  minimumSize: const Size(70, 32),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                child: Text(
                  isLocked ? 'Locked' : 'Start',
                  style: const TextStyle(fontSize: 12),
                ),
              )
            : IconButton(
                onPressed: () => _viewResults(test),
                icon: const Icon(Icons.analytics),
                tooltip: 'View Results',
              ),
        onTap: isAvailable && !isLocked ? () => _showTestDetails(test) : null,
      ),
    ).animate(delay: Duration(milliseconds: 100 * index))
        .fadeIn()
        .slideX(begin: 0.2);
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.quiz_outlined,
            size: 48,
            color: AppTheme.textTertiary,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: AppTheme.headingSmall.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getTestTypeColor(String type) {
    switch (type) {
      case 'JEE Main':
        return Colors.blue;
      case 'JEE Advanced':
        return Colors.purple;
      case 'NEET':
        return Colors.green;
      default:
        return AppTheme.primaryColor;
    }
  }

  Color _getSubjectColor(String subject) {
    switch (subject.toLowerCase()) {
      case 'physics':
        return Colors.blue;
      case 'chemistry':
        return Colors.green;
      case 'mathematics':
        return Colors.orange;
      case 'biology':
        return Colors.purple;
      default:
        return AppTheme.primaryColor;
    }
  }

  void _startTest(Map<String, dynamic> test) {
    AppLogger.userAction('Mock test started', {'test': test['title']});
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Start ${test['title']}?'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Duration: ${test['duration']} minutes'),
            Text('Questions: ${test['questions']}'),
            Text('Max Marks: ${test['maxMarks']}'),
            const SizedBox(height: 8),
            const Text(
              'Make sure you have a stable internet connection and won\'t be disturbed.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Starting ${test['title']}...'),
                  behavior: SnackBarBehavior.floating,
                  backgroundColor: _getTestTypeColor(test['type']),
                ),
              );
            },
            child: const Text('Start Test'),
          ),
        ],
      ),
    );
  }

  void _viewResults(Map<String, dynamic> test) {
    AppLogger.userAction('Test results viewed', {'test': test['title']});
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Viewing results for ${test['title']}'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showTestDetails(Map<String, dynamic> test) {
    AppLogger.userAction('Test details viewed', {'test': test['title']});
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: AppTheme.textTertiary,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    test['title'],
                    style: AppTheme.headingMedium.copyWith(
                      color: AppTheme.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Test details, syllabus coverage, and preparation tips will be available here.',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
