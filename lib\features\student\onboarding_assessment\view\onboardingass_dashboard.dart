import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Assume a provider for fetching onboard data
final onboardDataProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  // Fetch data using Dio similar to above
  return {
    'total_questions': 20,
    'correct_answers': 15,
    'incorrect_answers': 5,
    'mental_ability': 8,
    'logical_reasoning': 12,
  };
});

class OnboardAssessmentDashboard extends ConsumerWidget {
  const OnboardAssessmentDashboard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dataAsync = ref.watch(onboardDataProvider);

    return dataAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (err, stack) => Center(child: Text('Error: $err')),
      data: (data) {
        if (data.isEmpty) return const Center(child: Text('No data found'));

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Overview Chart
              Card(
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                elevation: 4,
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: AspectRatio(
                    aspectRatio: 1.8,
                    child: BarChart(
                      BarChartData(
                        titlesData: FlTitlesData(show: false),
                        borderData: FlBorderData(show: false),
                        barGroups: [
                          BarChartGroupData(x: 0, barRods: [
                            BarChartRodData(
                              toY: (data['total_questions'] ?? 0).toDouble(),
                              color: Colors.blue,
                              width: 16,
                            ),
                            BarChartRodData(
                              toY: (data['correct_answers'] ?? 0).toDouble(),
                              color: Colors.green,
                              width: 16,
                            ),
                            BarChartRodData(
                              toY: (data['incorrect_answers'] ?? 0).toDouble(),
                              color: Colors.red,
                              width: 16,
                            ),
                          ]),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Topic Breakdown Chart
              Card(
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                elevation: 4,
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: AspectRatio(
                    aspectRatio: 1.8,
                    child: BarChart(
                      BarChartData(
                        titlesData: FlTitlesData(show: false),
                        borderData: FlBorderData(show: false),
                        barGroups: [
                          BarChartGroupData(x: 0, barRods: [
                            BarChartRodData(
                              toY: (data['MENTAL_ABILITY'] ?? 0).toDouble(),
                              color: Colors.purple,
                              width: 20,
                            ),
                            BarChartRodData(
                              toY: (data['LOGICAL_ABILITY'] ?? 0).toDouble(),
                              color: Colors.orange,
                              width: 20,
                            ),
                          ]),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
