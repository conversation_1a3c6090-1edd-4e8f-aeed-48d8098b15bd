import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:confetti/confetti.dart';
import 'package:desktop_drop/desktop_drop.dart';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:file_saver/file_saver.dart'; 
import '../model/question_generator_model.dart';
import '../../../../core/config/app_config.dart';

class QuestionGeneratorPage extends StatefulWidget {
  const QuestionGeneratorPage({super.key, this.baseUrl});

  final String? baseUrl;

  @override
  State<QuestionGeneratorPage> createState() => _PdfQuestionGeneratorPageState();
}

class _PdfQuestionGeneratorPageState extends State<QuestionGeneratorPage>
    with SingleTickerProviderStateMixin {
  final Dio _dio = Dio();
  final ValueNotifier<bool> _isDragging = ValueNotifier(false);
  final ConfettiController _confetti =
      ConfettiController(duration: const Duration(milliseconds: 1500));

  PlatformFile? _file;
  String? _fileName;
  String? _userId;
  bool _loading = false;
  String? _error;
  bool _expanded = false;
  bool _bookmarked = false;

  ApiResponse? _response;

  late final AnimationController _pop;

  @override
  void initState() {
    super.initState();
    _pop = AnimationController(vsync: this, duration: 300.ms)..forward();
    _loadUserId();
  }

  @override
  void dispose() {
    _isDragging.dispose();
    _confetti.dispose();
    _pop.dispose();
    super.dispose();
  }

  Future<void> _loadUserId() async {
    final prefs = await SharedPreferences.getInstance();
    final rawUserData = prefs.getString('user_data');

    if (rawUserData != null) {
      try {
        final Map<String, dynamic> userData = jsonDecode(rawUserData);
        final id = userData['id']?.toString();
        setState(() {
          _userId = id;
        });
      } catch (e) {
        debugPrint('Error parsing user data: $e');
        setState(() => _userId = null);
      }
    } else {
      setState(() => _userId = null);
    }
  }

  bool get _fileTypeOk {
    if (_file == null) return false;
    final name = _file!.name.toLowerCase();
    return name.endsWith('.pdf') ||
        name.endsWith('.jpg') ||
        name.endsWith('.jpeg') ||
        name.endsWith('.png');
  }

  Future<void> _pickFile() async {
    setState(() => _error = null);
    final res = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png'],
      withData: kIsWeb,
    );
    if (res != null && res.files.isNotEmpty) {
      final f = res.files.first;
      if (!_isSupported(f)) {
        setState(
            () => _error = 'Please upload a PDF, JPG, JPEG, or PNG file only');
        return;
      }
      setState(() {
        _file = f;
        _fileName = f.name;
      });
    }
  }

  bool _isSupported(PlatformFile f) {
    final n = f.name.toLowerCase();
    return n.endsWith('.pdf') ||
        n.endsWith('.jpg') ||
        n.endsWith('.jpeg') ||
        n.endsWith('.png');
  }

  Future<void> _handleSubmit() async {
    if (_file == null || _userId == null || _userId!.isEmpty) {
      setState(() =>
          _error = 'Please upload a file and ensure user ID is available');
      return;
    }
    setState(() {
      _loading = true;
      _error = null;
    });

    try {
      final uri = '${AppConfig.apiBaseUrl}${AppConfig.uploadQuestionEndpoint}';

      final formData = FormData();
      formData.fields.add(MapEntry('user_id', _userId!));
      if (kIsWeb) {
        final bytes = _file!.bytes!;
        formData.files.add(MapEntry(
          'file',
          MultipartFile.fromBytes(bytes, filename: _file!.name),
        ));
      } else {
        formData.files.add(MapEntry(
          'file',
          await MultipartFile.fromFile(_file!.path!, filename: _file!.name),
        ));
      }

      final resp = await _dio.postUri(Uri.parse(uri), data: formData);
      final data = resp.data is Map<String, dynamic>
          ? resp.data as Map<String, dynamic>
          : jsonDecode(resp.data as String) as Map<String, dynamic>;

      setState(() => _response = ApiResponse.fromJson(data));
    } on DioException catch (e) {
      setState(() => _error =
          _extractError(e) ?? 'Failed to process file. Please try again.');
    } catch (e) {
      setState(() => _error = 'Failed to process file. Please try again.');
    } finally {
      if (mounted) setState(() => _loading = false);
    }
  }

  String? _extractError(DioException e) {
    try {
      if (e.response?.data is Map<String, dynamic>) {
        return (e.response!.data as Map<String, dynamic>)['error']?.toString();
      }
      if (e.response?.data is String) {
        final m =
            jsonDecode(e.response!.data as String) as Map<String, dynamic>;
        return m['error']?.toString();
      }
    } catch (_) {}
    return null;
  }

  Future<void> _handleDownloadPdf(int questionId) async {
    try {
    
      final uri = '${AppConfig.apiBaseUrl}${AppConfig.generatePdfEndpoint}/$questionId'; 
      debugPrint('Constructed URL: $uri');
      debugPrint('UserId for auth: $_userId');
      final resp = await _dio.getUri(
        Uri.parse(uri),
        options: Options(responseType: ResponseType.bytes),
      );
      final bytes = resp.data as Uint8List;

      final filename = 'questions_$questionId.pdf';

      if (kIsWeb) {
        // Use file_saver to download the PDF on web
        await FileSaver.instance.saveFile(
          name: filename,
          bytes: bytes,
          ext: 'pdf',
          mimeType: MimeType.pdf,
        );
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('PDF downloaded successfully!'),
            ),
          );
        }
      } else {
        final dir = await getTemporaryDirectory();
        final file = File('${dir.path}/$filename');
        await file.writeAsBytes(bytes, flush: true);
        await OpenFilex.open(file.path);
      }

      _confetti.play();
    } catch (e) {
      setState(() => _error = 'Failed to download PDF');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final canSubmit = _file != null && _fileTypeOk && !_loading;
    return Scaffold(
      backgroundColor: const Color(0xFFF1F5FE),
      body: Stack(
        children: [
          Positioned.fill(
            child: IgnorePointer(
              ignoring: true,
              child: CustomPaint(
                painter: _BlobPainter(),
              ),
            ),
          ),
          Center(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 980),
                child: _CardShell(
                  header: _Header(pop: _pop),
                  child: SingleChildScrollView(
                    child: _buildBody(theme, canSubmit),
                  ),
                ),
              ),
            ),
          ),
          
        ],
      ),
    );
  }

  Widget _buildBody(ThemeData theme, bool canSubmit) {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          16.heightBox,
          _buildDropZone(),
          if (_fileName != null) 12.heightBox,
          if (_fileName != null)
            _SelectedFileTile(
              fileName: _fileName!,
              sizeLabel: _fileSizeLabel(_file!),
              bookmarked: _bookmarked,
              onToggleBookmark: () => setState(() => _bookmarked = !_bookmarked),
              onClear: () => setState(() {
                _file = null;
                _fileName = null;
              }),
            ),
          if (_error != null) 12.heightBox,
          if (_error != null)
            _ErrorBanner(
              error: _error!,
              onClose: () => setState(() => _error = null),
            ),
          14.heightBox,
          _PrimaryButton(
            enabled: canSubmit,
            loading: _loading,
            icon: Icons.auto_awesome,
            label: 'Generate Questions',
            onPressed: _handleSubmit,
          ),
          14.heightBox,
          if (_response != null)
            _ResultsCard(
              response: _response!,
              expanded: _expanded,
              onToggleExpand: () => setState(() => _expanded = !_expanded),
              onDownload: () => _handleDownloadPdf(_response!.questionId),
            ),
        ],
      ),
    );
  }

  Widget _buildDropZone() {
    final dropChild = _DropSurface(
      isDraggingListenable: _isDragging,
      onTapPick: _pickFile,
      title: _isDragging.value
          ? 'Drop your file here'
          : (_fileName ??
              'Drag & drop your PDF or image (JPG, JPEG, PNG) or click to browse'),
      subtitle: 'Supports PDF, JPG, JPEG, PNG files up to 10MB',
    );

    return kIsWeb || !Platform.isAndroid && !Platform.isIOS
        ? DropTarget(
            onDragEntered: (_) => _isDragging.value = true,
            onDragExited: (_) => _isDragging.value = false,
            onDragDone: (details) async {
              _isDragging.value = false;
              if (details.files.isEmpty) return;
              final x = details.files.first;
              if (kIsWeb)
                return; // handled by file_picker web; desktop_drop web path differs
              final path = x.path;
              if (path == null) return;
              final f = PlatformFile(
                  name: path.split('/').last,
                  path: path,
                  size: await File(path).length());
              if (!_isSupported(f)) {
                setState(() => _error =
                    'Please upload a PDF, JPG, JPEG, or PNG file only');
                return;
              }
              setState(() {
                _file = f;
                _fileName = f.name;
                _error = null;
              });
            },
            child: dropChild,
          )
        : dropChild;
  }

  String _fileSizeLabel(PlatformFile f) {
    final mb = (f.size / 1024 / 1024);
    return '${mb.toStringAsFixed(2)} MB';
  }
}

class _CardShell extends StatelessWidget {
  const _CardShell({required this.header, required this.child});
  final Widget header;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: 300.ms,
      curve: Curves.easeOut,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(28),
        border: Border.all(color: Colors.white.withOpacity(.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(.15),
            blurRadius: 40,
            spreadRadius: -4,
            offset: const Offset(0, 18),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0xFF2563EB), Color(0xFF4F46E5)],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              borderRadius: BorderRadius.vertical(top: Radius.circular(28)),
            ),
            child: header,
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Padding(padding: const EdgeInsets.all(16), child: child),
            ),
          ),
        ],
      ),
    )
        .animate()
        .fade(duration: 400.ms)
        .scale(begin: const Offset(.98, .98), curve: Curves.easeOut);
  }
}

class _Header extends StatelessWidget {
  const _Header({required this.pop});
  final AnimationController pop;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(.12),
                  border: Border.all(color: Colors.white.withOpacity(.24)),
                  borderRadius: BorderRadius.circular(14),
                  boxShadow: [
                    BoxShadow(color: Colors.black.withOpacity(.05), blurRadius: 8)
                  ],
                ),
                child:
                    const Icon(Icons.description, color: Colors.white, size: 16),
              )
                  .animate(controller: pop)
                  .scale(begin: const Offset(.9, .9))
                  .rotate(begin: -.05, end: 0),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    'Question Generator',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.white, fontWeight: FontWeight.w700),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  SizedBox(
                    width: 200,
                    child: Text(
                      'Transform your PDFs or images into practice questions',
                      style: Theme.of(context)
                          .textTheme
                          .bodySmall
                          ?.copyWith(color: Colors.white.withOpacity(.9)),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              )
            ],
          ),
        ],
      ),
    );
  }
}

class _DropSurface extends StatelessWidget {
  const _DropSurface({
    required this.isDraggingListenable,
    required this.onTapPick,
    required this.title,
    required this.subtitle,
  });

  final ValueListenable<bool> isDraggingListenable;
  final VoidCallback onTapPick;
  final String title;
  final String subtitle;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: isDraggingListenable,
      builder: (context, dragging, _) {
        final borderColor = dragging ? Colors.blue : Colors.grey.shade300;
        final bg =
            dragging ? const Color(0xFFEFF6FF) : Colors.white.withOpacity(.7);
        return AnimatedScale(
          duration: 150.ms,
          scale: dragging ? 1.01 : 1.0,
          child: InkWell(
            onTap: onTapPick,
            borderRadius: BorderRadius.circular(20),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
              decoration: BoxDecoration(
                color: bg,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                    color: borderColor, width: 2, style: BorderStyle.solid),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  AnimatedRotation(
                    duration: 150.ms,
                    turns: dragging ? .02 : 0,
                    child: Icon(Icons.cloud_upload,
                        size: 48, color: dragging ? Colors.blue : Colors.grey),
                  ),
                  const SizedBox(height: 12),
                  Text(title,
                      style: const TextStyle(
                          fontSize: 16, fontWeight: FontWeight.w600)),
                  const SizedBox(height: 6),
                  Text(subtitle, style: TextStyle(color: Colors.grey.shade600)),
                  const SizedBox(height: 10),
                  _GhostButton(
                      icon: Icons.search,
                      label: 'Select File',
                      onTap: onTapPick),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class _GhostButton extends StatelessWidget {
  const _GhostButton(
      {required this.icon, required this.label, required this.onTap});
  final IconData icon;
  final String label;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(24),
      child: Ink(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
              colors: [Color(0xFF3B82F6), Color(0xFF2563EB)]),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
                color: Colors.blue.withOpacity(.25),
                blurRadius: 16,
                offset: const Offset(0, 8)),
          ],
        ),
        child: Row(mainAxisSize: MainAxisSize.min, children: [
          Icon(icon, color: Colors.white, size: 18),
          const SizedBox(width: 8),
          Text(label,
              style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w600)),
        ]),
      ),
    );
  }
}

class _SelectedFileTile extends StatelessWidget {
  const _SelectedFileTile({
    required this.fileName,
    required this.sizeLabel,
    required this.bookmarked,
    required this.onToggleBookmark,
    required this.onClear,
  });
  final String fileName;
  final String sizeLabel;
  final bool bookmarked;
  final VoidCallback onToggleBookmark;
  final VoidCallback onClear;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFEFF6FF),
        borderRadius: BorderRadius.circular(14),
        border: Border.all(color: const Color(0xFFDBEAFE)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
                color: const Color(0xFFDBEAFE),
                borderRadius: BorderRadius.circular(10)),
            child: const Icon(Icons.description,
                color: Color(0xFF2563EB), size: 18),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Text(fileName,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(fontWeight: FontWeight.w600)),
              Text(sizeLabel,
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 12)),
            ]),
          ),
          IconButton(
            onPressed: onToggleBookmark,
            icon: Icon(
                bookmarked ? Icons.bookmark_added : Icons.bookmark_border,
                color: const Color(0xFF2563EB)),
            tooltip: 'Bookmark',
          ),
          IconButton(
            onPressed: onClear,
            icon: const Icon(Icons.cancel_outlined, color: Colors.redAccent),
            tooltip: 'Remove',
          ),
        ],
      ),
    ).animate().slideY(begin: .1, duration: 250.ms).fadeIn(duration: 250.ms);
  }
}

class _ErrorBanner extends StatelessWidget {
  const _ErrorBanner({required this.error, required this.onClose});
  final String error;
  final VoidCallback onClose;

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: 250.ms,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFFEF2F2),
        borderRadius: BorderRadius.circular(14),
        border: Border.all(color: const Color(0xFFFECACA)),
      ),
      child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
              color: const Color(0xFFFEE2E2),
              borderRadius: BorderRadius.circular(20)),
          child: const Icon(Icons.error_outline, color: Colors.red, size: 18),
        ),
        const SizedBox(width: 10),
        Expanded(child: Text(error, style: const TextStyle(color: Colors.red))),
        IconButton(
            onPressed: onClose, icon: const Icon(Icons.close, color: Colors.red)),
      ]),
    ).animate().fadeIn().slideY(begin: -.05);
  }
}

class _PrimaryButton extends StatefulWidget {
  const _PrimaryButton({
    required this.enabled,
    required this.loading,
    required this.icon,
    required this.label,
    required this.onPressed,
  });

  final bool enabled;
  final bool loading;
  final IconData icon;
  final String label;
  final VoidCallback onPressed;

  @override
  _PrimaryButtonState createState() => _PrimaryButtonState();
}

class _PrimaryButtonState extends State<_PrimaryButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _rotationAnimation;
  late Animation<Offset> _gradientAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500), // Matches React's 1.5s
    )..repeat(); // Infinite rotation like animate-spin
    _rotationAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.linear, // Matches React's linear ease
      ),
    );
    _gradientAnimation = Tween<Offset>(
      begin: const Offset(-1, 0),
      end: const Offset(1, 0),
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.linear,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final canPress = widget.enabled && !widget.loading;
    return AnimatedOpacity(
      duration: const Duration(milliseconds: 200),
      opacity: canPress ? 1 : 0.6,
      child: GestureDetector(
        onTap: canPress ? widget.onPressed : null,
        child: Container(
          width: double.infinity, // Makes button full-width
          padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: widget.loading
                  ? [
                      const Color(0xFF2563EB),
                      const Color(0xFF4F46E5).withOpacity(0.7),
                      const Color(0xFF2563EB),
                    ]
                  : [
                      canPress ? const Color(0xFF2563EB) : Colors.grey.shade300,
                      canPress ? const Color(0xFF4F46E5) : Colors.grey.shade300,
                    ],
              begin: widget.loading
                  ? Alignment(_gradientAnimation.value.dx, 0)
                  : Alignment.centerLeft,
              end: widget.loading
                  ? Alignment(_gradientAnimation.value.dx + 2, 0)
                  : Alignment.centerRight,
            ),
            borderRadius: BorderRadius.circular(14),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withOpacity(canPress ? 0.25 : 0),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, _) => Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                widget.loading
                    ? RotationTransition(
                        turns: _rotationAnimation,
                        child: const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2.5,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                      )
                    : Icon(
                        widget.icon,
                        size: 20,
                        color: canPress ? Colors.white : Colors.grey.shade600,
                      ),
                const SizedBox(width: 8),
                Text(
                  widget.loading ? 'Processing...' : widget.label,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: canPress ? Colors.white : Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ).animate().scale(
          begin: const Offset(0.99, 0.99),
          end: const Offset(1.01, 1.01),
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
        );
  }
}

class _ResultsCard extends StatelessWidget {
  const _ResultsCard({
    required this.response,
    required this.expanded,
    required this.onToggleExpand,
    required this.onDownload,
  });
  final ApiResponse response;
  final bool expanded;
  final VoidCallback onToggleExpand;
  final VoidCallback onDownload;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: AnimatedContainer(
        duration: 250.ms,
        decoration: BoxDecoration(
          color: const Color(0xFFEBF5FF),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: const Color(0xFFD1E9FF)),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.6,
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                  color: const Color(0xFFD1FAE5),
                                  borderRadius: BorderRadius.circular(10)),
                              child: const Icon(Icons.check_circle,
                                  color: Color(0xFF059669)),
                            ),
                            const SizedBox(width: 10),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Generated Questions',
                                    style: const TextStyle(
                                        fontWeight: FontWeight.w700,
                                        fontSize: 18),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                                  Text(
                                    'Ready for download',
                                    style: TextStyle(
                                        color: Colors.grey.shade700,
                                        fontSize: 12),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: onToggleExpand,
                        icon: Icon(
                          expanded
                              ? Icons.keyboard_arrow_up
                              : Icons.keyboard_arrow_down,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                          child: _InfoTile(
                              label: 'Subject',
                              dotColor: Colors.blue,
                              value: response.subject)),
                      const SizedBox(width: 12),
                      Expanded(
                          child: _InfoTile(
                              label: 'Topic',
                              dotColor: Colors.lightBlue,
                              value: response.topic)),
                    ],
                  ),
                  if (expanded)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        const SizedBox(height: 12),
                        _BoxedSection(
                            title: 'Extracted Content',
                            color: Colors.amber,
                            child: Text(response.extractedQuestion,
                                    style: const TextStyle(height: 1.3))
                                .animate()
                                .fadeIn()),
                        const SizedBox(height: 12),
                        _BoxedSection(
                          title: 'Generated Questions',
                          color: Colors.green,
                          child: Column(
                            children: [
                              for (var i = 0; i < response.questions.length; i++)
                                _QuestionTile(q: response.questions[i])
                                    .animate()
                                    .fadeIn()
                                    .slideX(begin: -.05, duration: (100 + i * 40).ms),
                            ],
                          ),
                        ),
                      ],
                    ),
                  const SizedBox(height: 12),
                  _DownloadButton(onPressed: onDownload),
                ],
              ),
            ),
          ),
        ),
      ),
    ).animate().fadeIn().slideY(begin: .05);
  }
}
class _InfoTile extends StatelessWidget {
  const _InfoTile(
      {required this.label, required this.dotColor, required this.value});
  final String label;
  final Color dotColor;
  final String value;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(14),
          border: Border.all(color: Colors.grey.shade200)),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Row(children: [
          Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                  color: dotColor, borderRadius: BorderRadius.circular(8))),
          const SizedBox(width: 6),
          Text(label,
              style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12,
                  fontWeight: FontWeight.w600)),
        ]),
        const SizedBox(height: 4),
        Text(value,
            style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 16)),
      ]),
    );
  }
}

class _BoxedSection extends StatelessWidget {
  const _BoxedSection(
      {required this.title, required this.color, required this.child});
  final String title;
  final Color color;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(14),
          border: Border.all(color: Colors.grey.shade200)),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Row(children: [
          Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                  color: color, borderRadius: BorderRadius.circular(8))),
          const SizedBox(width: 6),
          Text(title,
              style: TextStyle(
                  color: Colors.grey.shade700, fontWeight: FontWeight.w600)),
        ]),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
              color: const Color(0xFFF8FAFC),
              borderRadius: BorderRadius.circular(10)),
          child: child,
        ),
      ]),
    );
  }
}

class _QuestionTile extends StatelessWidget {
  const _QuestionTile({required this.q});
  final Question q;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
          color: const Color(0xFFEFF6FF),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: const Color(0xFFDBEAFE))),
      child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
        const Icon(Icons.stars, color: Color(0xFF2563EB), size: 18),
        const SizedBox(width: 8),
        Expanded(
          child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            RichText(
              text: TextSpan(
                style: DefaultTextStyle.of(context).style,
                children: [
                  TextSpan(
                      text: '${q.id}. ',
                      style: const TextStyle(
                          color: Color(0xFF2563EB),
                          fontWeight: FontWeight.w700)),
                  TextSpan(
                      text: q.questionText,
                      style: const TextStyle(fontWeight: FontWeight.w600)),
                ],
              ),
            ),
            const SizedBox(height: 8),
            LayoutBuilder(builder: (context, c) {
              final twoCol = c.maxWidth > 520;
              final entries = q.options.entries.toList();
              return Wrap(
                alignment: WrapAlignment.start,
                runSpacing: 6,
                spacing: 6,
                children: entries.map((e) {
                  final isCorrect = q.correctAnswer == e.key;
                  return Container(
                    width: twoCol ? (c.maxWidth - 6) / 2 : c.maxWidth,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                    decoration: BoxDecoration(
                      color: isCorrect
                          ? const Color(0xFFD1FAE5)
                          : const Color(0xFFF1F5F9),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                          color: isCorrect
                              ? const Color(0xFF86EFAC)
                              : const Color(0xFFE2E8F0)),
                    ),
                    child: Text('${e.key}: ${e.value}'),
                  );
                }).toList(),
              );
            }),
            const SizedBox(height: 6),
            Text.rich(
                TextSpan(children: [
                  TextSpan(
                      text: 'Solution: ',
                      style: TextStyle(
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w700)),
                  TextSpan(text: q.solution),
                ]),
                style: const TextStyle(fontSize: 13)),
          ]),
        ),
      ]),
    );
  }
}

class _DownloadButton extends StatelessWidget {
  const _DownloadButton({required this.onPressed});
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 14),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
              colors: [Color(0xFF10B981), Color(0xFF059669)]),
          borderRadius: BorderRadius.circular(14),
          boxShadow: [
            BoxShadow(
                color: Colors.green.withOpacity(.25),
                blurRadius: 18,
                offset: const Offset(0, 10))
          ],
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Row(mainAxisSize: MainAxisSize.min, children: const [
              Icon(Icons.cloud_download, color: Colors.white),
              SizedBox(width: 8),
              Text('Download Question PDF',
                  style: TextStyle(color: Colors.white, fontWeight: FontWeight.w700)),
            ]),
            Positioned.fill(
              child: AnimatedSlide(
                duration: 1800.ms,
                offset: const Offset(1.2, 0),
                curve: Curves.linear,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.white.withOpacity(0),
                        Colors.white.withOpacity(.3),
                        Colors.white.withOpacity(0)
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                    borderRadius: BorderRadius.circular(14),
                  ),
                ),
              ).animate(onPlay: (c) => c.repeat()),
            ),
          ],
        ),
      ),
    )
        .animate()
        .scale(begin: const Offset(.99, .99), end: const Offset(1.01, 1.01));
  }
}

class _BlobPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final rnd = _PseudoRandom(42);
    for (int i = 0; i < 10; i++) {
      final w = rnd.nextDouble() * 200 + 100;
      final h = rnd.nextDouble() * 200 + 100;
      final left = rnd.nextDouble() * (size.width - w);
      final top = rnd.nextDouble() * (size.height - h);
      final rect = Rect.fromLTWH(left, top, w, h);
      final r = RRect.fromRectAndRadius(rect, const Radius.circular(999));
      final paint = Paint()
        ..color = const Color(0xFF93C5FD).withOpacity(0.12)
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 40);
      canvas.drawRRect(r, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _PseudoRandom {
  _PseudoRandom(this.seed);
  int seed;
  double nextDouble() {
    seed = 1664525 * seed + 1013904223;
    return ((seed % 0xFFFFFF) / 0xFFFFFF);
  }
}

extension on num {
  SizedBox get heightBox => SizedBox(height: toDouble());
  SizedBox get widthBox => SizedBox(width: toDouble());
}