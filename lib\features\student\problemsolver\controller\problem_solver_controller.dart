import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_sound_record/flutter_sound_record.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:dio/dio.dart';
import '../../../../core/config/app_config.dart'; // Make sure this path is correct
import '../../../../core/services/api_service.dart' as coreApi;
import '../../../../core/exceptions/api_exceptions.dart';
import '../model/problem_solver_model.dart';
import '../model/search_result_models.dart';
import 'package:url_launcher/url_launcher.dart';

// Enum to define the type of search to perform
enum SearchType { doubtSolver, youtubeSearch, webSearch }

class ProblemSolverController extends ChangeNotifier {
  final coreApi.ApiService _apiService = coreApi.ApiService();
  final ImagePicker _picker = ImagePicker();
  final FlutterSoundRecord _audioRecorder = FlutterSoundRecord();

  String _response = '';
  bool _isLoading = false;
  String _error = '';
  List<dynamic>? _history;
  String _doubtText = '';
  File? _imageFile;
  String? _audioPath;
  bool _isRecording = false;
  String _selectedMode = 'tutor';
  String _selectedLanguage = 'english';

    // New state for search results
  List<YoutubeSearchResult> _youtubeResults = [];
  List<WebSearchResult> _webResults = [];
  SearchType? _lastSearchType;

  final List<String> _modes = ['text', 'tutor', 'conversation'];
  final List<String> _languages = [
    'english',
    'tanglish',
    'hinglish',
    'manglish',
    'kanglish',
    'tenglish'
  ];

  // Properties from your previous code
  String get response => _response;
  bool get isLoading => _isLoading;
  String get error => _error;
  List<dynamic>? get history => _history;
  String get doubtText => _doubtText;
  File? get imageFile => _imageFile;
  String? get audioPath => _audioPath;
  bool get isRecording => _isRecording;
  String get selectedMode => _selectedMode;
  String get selectedLanguage => _selectedLanguage;
  List<String> get modes => _modes;
  List<String> get languages => _languages;
  List<YoutubeSearchResult> get youtubeResults => _youtubeResults;
  List<WebSearchResult> get webResults => _webResults;
  SearchType? get lastSearchType => _lastSearchType;

  void setDoubtText(String text) {
    _doubtText = text;
    notifyListeners();
  }

  void setMode(String? newMode) {
    if (newMode != null) {
      _selectedMode = newMode;
      notifyListeners();
    }
  }

  void setLanguage(String? newLanguage) {
    if (newLanguage != null) {
      _selectedLanguage = newLanguage;
      notifyListeners();
    }
  }

  /// Fetches the user ID from SharedPreferences.
  /// This logic is based on your _loadUserData method.
Future<String> _getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    final userDataString = prefs.getString(AppConfig.userDataKey);
    if (userDataString != null) {
      final userData = json.decode(userDataString);
      return userData['id']?.toString() ?? 'default_user';
    }
    return 'default_user';
  }

  void clearAttachments() {
    _imageFile = null;
    if (_audioPath != null) {
      File(_audioPath!).delete().catchError((_) {});
      _audioPath = null;
    }
    notifyListeners();
  }

  Future<void> pickImageFromGallery() async {
    final XFile? pickedFile =
        await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      clearAttachments();
      _imageFile = File(pickedFile.path);
      notifyListeners();
    }
  }

  Future<void> captureImageWithCamera() async {
    final XFile? pickedFile =
        await _picker.pickImage(source: ImageSource.camera);
    if (pickedFile != null) {
      clearAttachments();
      _imageFile = File(pickedFile.path);
      notifyListeners();
    }
  }

  Future<void> toggleAudioRecording() async {
    if (_isRecording) {
      await _stopRecording();
    } else {
      await _startRecording();
    }
  }

  Future<void> _startRecording() async {
    if (await Permission.microphone.request().isGranted) {
      clearAttachments();
      _isRecording = true;
      notifyListeners();
      try {
        await _audioRecorder.start(
            path: '${Directory.systemTemp.path}/temp_audio.m4a');
      } catch (e) {
        _isRecording = false;
        _error = 'Failed to start recording: $e';
        notifyListeners();
      }
    } else {
      _error = 'Microphone permission is required to record audio.';
      notifyListeners();
    }
  }

  Future<void> _stopRecording() async {
    _isRecording = false;
    try {
      final path = await _audioRecorder.stop();
      if (path != null) {
        _audioPath = path;
      }
    } catch (e) {
      _error = 'Failed to stop recording: $e';
    }
    notifyListeners();
  }


  void _clearPreviousResults() {
    _response = '';
    _history = null;
    _youtubeResults = [];
    _webResults = [];
    _error = '';
  }

  Future<void> performSearch(SearchType searchType) async {
    if (_doubtText.isEmpty && _imageFile == null && _audioPath == null) {
      _error = 'Please provide input via text, image, or audio.';
      notifyListeners();
      return;
    }

    _isLoading = true;
    _lastSearchType = searchType;
    _clearPreviousResults();
    notifyListeners();

    try {
      final String userId = await _getUserId();
      Map<String, dynamic> result;

      switch (searchType) {
        case SearchType.doubtSolver:
          result = await _apiService.solveDoubt(
            text: _doubtText,
            mode: _selectedMode,
            language: _selectedLanguage,
            userId: userId,
            imageFile: _imageFile,
            audioFile: _audioPath != null ? File(_audioPath!) : null,
          );
          final doubtResponse = DoubtResponse.fromJson(result);
          _response = doubtResponse.response;
          _history = doubtResponse.history;
          break;

        case SearchType.youtubeSearch:
          result = await _apiService.youtubeSearch(
            text: _doubtText,
            userId: userId,
            imageFile: _imageFile,
            audioFile: _audioPath != null ? File(_audioPath!) : null,
          );
          final resultsList = result['videos'] as List? ?? [];
          _youtubeResults = resultsList.map((data) => YoutubeSearchResult.fromJson(data)).toList();
          break;

        case SearchType.webSearch:
          result = await _apiService.webSearch(
            text: _doubtText,
            userId: userId,
            imageFile: _imageFile,
            audioFile: _audioPath != null ? File(_audioPath!) : null,
          );
          final resultsList = result['results'] as List? ?? [];
          _webResults = resultsList.map((data) => WebSearchResult.fromJson(data)).toList();
          break;
      }
    } on ApiException catch (e) {
      _error = e.message;
    } catch (e) {
      _error = 'An unexpected error occurred: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }


  
  
   /// Launches a URL using the url_launcher package.
  Future<void> launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await canLaunchUrl(uri)) {
      _error = 'Could not launch $url';
      notifyListeners();
      return;
    }
    await launchUrl(uri, mode: LaunchMode.externalApplication);
  }
  
   @override
  void dispose() {
    _audioRecorder.dispose();
    super.dispose();
  }
}