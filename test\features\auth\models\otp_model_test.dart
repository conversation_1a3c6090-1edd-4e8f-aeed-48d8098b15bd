import 'package:flutter_test/flutter_test.dart';
import 'package:sasthra_mobile_app/features/auth/models/otp_model.dart';

void main() {
  group('OtpModel Tests', () {
    late OtpModel otpModel;

    setUp(() {
      otpModel = OtpModel();
    });

    tearDown(() {
      otpModel.dispose();
    });

    test('should initialize with empty OTP digits', () {
      expect(otpModel.otpDigits, equals(['', '', '', '', '', '']));
      expect(otpModel.otpValue, isEmpty);
      expect(otpModel.isLoading, isFalse);
      expect(otpModel.errorMessage, isNull);
      expect(otpModel.resendCountdown, equals(0));
      expect(otpModel.canResend, isTrue);
      expect(otpModel.isOtpComplete, isFalse);
      expect(otpModel.isOtpValid, isFalse);
    });

    test('should set OTP digit at specific index', () {
      otpModel.setOtpDigit(0, '1');
      expect(otpModel.otpDigits[0], equals('1'));
      expect(otpModel.otpValue, equals('1'));
    });

    test('should not set OTP digit at invalid index', () {
      otpModel.setOtpDigit(-1, '1');
      otpModel.setOtpDigit(6, '1');
      expect(otpModel.otpValue, isEmpty);
    });

    test('should clear all OTP digits', () {
      // Set some digits first
      otpModel.setOtpDigit(0, '1');
      otpModel.setOtpDigit(1, '2');
      expect(otpModel.otpValue, equals('12'));

      otpModel.clearOtp();
      expect(otpModel.otpDigits, equals(['', '', '', '', '', '']));
      expect(otpModel.otpValue, isEmpty);
    });

    test('should check if OTP is complete', () {
      expect(otpModel.isOtpComplete, isFalse);

      // Set all digits
      for (int i = 0; i < 6; i++) {
        otpModel.setOtpDigit(i, (i + 1).toString());
      }

      expect(otpModel.isOtpComplete, isTrue);
      expect(otpModel.otpValue, equals('123456'));
    });

    test('should validate OTP correctly', () {
      // Empty OTP
      expect(otpModel.validateOtp(), isFalse);
      expect(otpModel.errorMessage, equals('Please enter complete OTP'));

      // Incomplete OTP
      otpModel.clearError();
      otpModel.setOtpDigit(0, '1');
      expect(otpModel.validateOtp(), isFalse);
      expect(otpModel.errorMessage, equals('Please enter complete OTP'));

      // Complete valid OTP
      otpModel.clearError();
      for (int i = 0; i < 6; i++) {
        otpModel.setOtpDigit(i, (i + 1).toString());
      }
      expect(otpModel.validateOtp(), isTrue);
      expect(otpModel.errorMessage, isNull);
    });

    test('should validate numeric OTP', () {
      // Set non-numeric characters
      for (int i = 0; i < 6; i++) {
        otpModel.setOtpDigit(i, 'a');
      }
      
      expect(otpModel.validateOtp(), isFalse);
      expect(otpModel.errorMessage, equals('OTP must contain only numbers'));
    });

    test('should start resend countdown', () {
      otpModel.startResendCountdown();
      expect(otpModel.resendCountdown, equals(60));
      expect(otpModel.canResend, isFalse);
    });

    test('should clear error message', () {
      otpModel.validateOtp(); // This will set an error
      expect(otpModel.errorMessage, isNotNull);

      otpModel.clearError();
      expect(otpModel.errorMessage, isNull);
    });

    test('should reset model state', () {
      // Set some state
      for (int i = 0; i < 6; i++) {
        otpModel.setOtpDigit(i, (i + 1).toString());
      }
      otpModel.startResendCountdown();
      
      otpModel.reset();
      
      expect(otpModel.otpDigits, equals(['', '', '', '', '', '']));
      expect(otpModel.isLoading, isFalse);
      expect(otpModel.errorMessage, isNull);
      expect(otpModel.resendCountdown, equals(0));
    });
  });
}
