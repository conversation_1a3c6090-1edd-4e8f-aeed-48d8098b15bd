import 'dart:async';
import 'package:flutter/material.dart';

class IncomingCallOverlay extends StatefulWidget {
  final Map<String, dynamic> incomingCall;
  final VoidCallback onAccept;
  final VoidCallback onReject;
  final bool isLoading;

  const IncomingCallOverlay({
    super.key,
    required this.incomingCall,
    required this.onAccept,
    required this.onReject,
    required this.isLoading,
  });

  @override
  State<IncomingCallOverlay> createState() => _IncomingCallOverlayState();
}

class _IncomingCallOverlayState extends State<IncomingCallOverlay> with TickerProviderStateMixin {
  late AnimationController _vibrateController;
  late Animation<double> _vibrateAnimation;
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;
  String _currentTime = '';
  Timer? _timeUpdateTimer;
  Timer? _callTimeoutTimer;
  int _remainingSeconds = 30; // 30 second timeout for incoming calls

  @override
  void initState() {
    super.initState();
    print('Initializing IncomingCallOverlay...');
    _updateTime();
    _timeUpdateTimer = Timer.periodic(const Duration(minutes: 1), (_) => _updateTime());

    // Start call timeout timer
    _startCallTimeout();

    try {
      _vibrateController = AnimationController(vsync: this, duration: const Duration(milliseconds: 800));
      _vibrateAnimation = TweenSequence<double>([
        TweenSequenceItem(tween: Tween<double>(begin: 0, end: -5), weight: 20),
        TweenSequenceItem(tween: Tween<double>(begin: -5, end: 0), weight: 20),
        TweenSequenceItem(tween: Tween<double>(begin: 0, end: 5), weight: 20),
        TweenSequenceItem(tween: Tween<double>(begin: 5, end: 0), weight: 20),
      ]).animate(_vibrateController);
      _vibrateController.repeat(reverse: true);

      _scaleController = AnimationController(vsync: this, duration: const Duration(seconds: 2));
      _scaleAnimation = Tween<double>(begin: 1, end: 1.1).animate(
        CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut),
      );
      _scaleController.repeat(reverse: true);
    } catch (e) {
      print('Error initializing animations: $e');
    }
  }

  void _startCallTimeout() {
    _callTimeoutTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        _remainingSeconds--;
      });

      if (_remainingSeconds <= 0) {
        timer.cancel();
        print('Call timeout reached, auto-rejecting call');
        widget.onReject(); // Auto-reject the call
      }
    });
  }

  void _updateTime() {
    if (!mounted) return;
    final now = DateTime.now();
    setState(() {
      _currentTime = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
      print('Updated current time: $_currentTime');
    });
  }

  @override
  void dispose() {
    print('Disposing IncomingCallOverlay...');
    _timeUpdateTimer?.cancel();
    _callTimeoutTimer?.cancel();
    try {
      _vibrateController.stop();
      _vibrateController.dispose();
      _scaleController.stop();
      _scaleController.dispose();
    } catch (e) {
      print('Error disposing animations: $e');
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print('Building IncomingCallOverlay...');
    final studentId = widget.incomingCall['student_id']?.toString() ?? 'Unknown';
    final roomName = widget.incomingCall['room_name']?.toString() ?? 'Unknown';

    return Positioned(
      bottom: 24,
      right: 24,
      child: Container(
        width: 320,
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(color: Colors.black.withOpacity(0.5), blurRadius: 50, offset: const Offset(0, 25)),
            BoxShadow(color: Colors.white.withOpacity(0.1), blurRadius: 0, spreadRadius: 0.5),
          ],
        ),
        child: Column(
          children: [
            // Phone Status Bar
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Row(
                    children: [
                      Icon(Icons.signal_cellular_alt, color: Colors.white, size: 12),
                      SizedBox(width: 4),
                      Icon(Icons.wifi, color: Colors.white, size: 12),
                    ],
                  ),
                  Text(_currentTime, style: const TextStyle(color: Colors.white, fontSize: 12)),
                  const Icon(Icons.battery_full, color: Colors.white, size: 12),
                ],
              ),
            ),
            // Phone Screen
            Container(
              height: 384,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Colors.indigo.shade900, Colors.purple.shade900, Colors.pink.shade900],
                ),
              ),
              child: Stack(
                children: [
                  // Background Pattern
                  Positioned.fill(
                    child: Opacity(
                      opacity: 0.2,
                      child: Stack(
                        children: [
                          Positioned(
                            top: 96,
                            left: 80,
                            child: Container(
                              width: 128,
                              height: 128,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(color: Colors.white),
                              ),
                            ),
                          ),
                          Positioned(
                            bottom: 96,
                            right: 80,
                            child: Container(
                              width: 96,
                              height: 96,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(color: Colors.white),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Vibrating Content
                  Center(
                    child: AnimatedBuilder(
                      animation: _vibrateController,
                      builder: (context, child) {
                        return Transform.translate(
                          offset: Offset(0, _vibrateAnimation.value),
                          child: child,
                        );
                      },
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ScaleTransition(
                            scale: _scaleAnimation,
                            child: Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                shape: BoxShape.circle,
                                border: Border.all(color: Colors.white.withOpacity(0.3)),
                              ),
                              child: const Icon(Icons.phone, color: Colors.white, size: 40),
                            ),
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'Incoming Call',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Student ID: $studentId',
                            style: const TextStyle(color: Colors.white70),
                          ),
                          Text(
                            'Room: $roomName',
                            style: const TextStyle(color: Colors.white60, fontSize: 12),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Auto-decline in ${_remainingSeconds}s',
                            style: TextStyle(
                              color: _remainingSeconds <= 10 ? Colors.red.shade300 : Colors.white70,
                              fontSize: 11,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.greenAccent,
                                ),
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                'Ringing',
                                style: TextStyle(color: Colors.greenAccent, fontSize: 12),
                              ),
                              const SizedBox(width: 8),
                              Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.greenAccent,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.greenAccent,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Call Action Buttons
                  Positioned(
                    bottom: 48,
                    left: 32,
                    right: 32,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              print('Decline call button tapped');
                              widget.onReject();
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              decoration: BoxDecoration(
                                color: Colors.red.shade600,
                                borderRadius: BorderRadius.circular(999),
                              ),
                              child: Column(
                                children: [
                                  widget.isLoading
                                      ? const CircularProgressIndicator(color: Colors.white)
                                      : const Icon(
                                          Icons.phone_disabled,
                                          color: Colors.white,
                                          size: 20,
                                        ),
                                  const SizedBox(height: 8),
                                  const Text(
                                    'Decline',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 24),
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              print('Accept call button tapped');
                              widget.onAccept();
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              decoration: BoxDecoration(
                                color: Colors.green.shade600,
                                borderRadius: BorderRadius.circular(999),
                              ),
                              child: Column(
                                children: [
                                  widget.isLoading
                                      ? const CircularProgressIndicator(color: Colors.white)
                                      : const Icon(
                                          Icons.phone,
                                          color: Colors.white,
                                          size: 20,
                                        ),
                                  const SizedBox(height: 8),
                                  const Text(
                                    'Accept',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Phone Bottom
            Container(
              height: 16,
              color: Colors.black,
              child: Center(
                child: Container(
                  width: 64,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade700,
                    borderRadius: BorderRadius.circular(999),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
