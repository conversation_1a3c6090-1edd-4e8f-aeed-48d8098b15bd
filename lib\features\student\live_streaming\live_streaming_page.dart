import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart' show debugPrint;
import '../../../core/config/app_config.dart';
import 'widgets/livekit_video_widget_stub.dart'
    if (dart.library.io) 'widgets/livekit_video_widget.dart';

class LiveStreamingScreen extends StatefulWidget {
  const LiveStreamingScreen({super.key});

  @override
  State<LiveStreamingScreen> createState() => _LiveStreamingScreenState();
}

class _LiveStreamingScreenState extends State<LiveStreamingScreen>
    with WidgetsBindingObserver, AutomaticKeepAliveClientMixin {
  bool isLoading = false;
  bool isJoining = false;
  bool isSendingMessage = false;
  bool isViewingStream = false;
  bool isChatOpen = false;
  int unreadMessages = 0;

  final TextEditingController _sessionIdController = TextEditingController();
  List<Map<String, dynamic>> activeStreams = [];
  Map<String, dynamic>? currentStream;
  List<Map<String, dynamic>> chatMessages = [];
  Timer? _chatTimer;
  Timer? _streamsTimer;
  Timer? _connectionHealthTimer;
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _chatScrollController = ScrollController();

  bool _isDisposed = false;
  bool _isInBackground = false;
  int _retryAttempts = 0;
  static const int _maxRetryAttempts = 3;

  @override
  bool get wantKeepAlive => isViewingStream;

  @override
  void initState() {
    super.initState();
    debugPrint('LiveStreamingScreen: initState called');
    WidgetsBinding.instance.addObserver(this);
    _initializeScreen();
  }

  @override
  void dispose() {
    debugPrint('LiveStreamingScreen: dispose called');
    _isDisposed = true;
    WidgetsBinding.instance.removeObserver(this);
    _cleanupResources();
    // Reset to default UI mode and orientation
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    debugPrint('LiveStreamingScreen: AppLifecycleState changed to $state');

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        debugPrint('LiveStreamingScreen: App paused or inactive, pausing operations');
        _isInBackground = true;
        _pauseBackgroundOperations();
        break;
      case AppLifecycleState.resumed:
        debugPrint('LiveStreamingScreen: App resumed, resuming operations');
        _isInBackground = false;
        _resumeForegroundOperations();
        break;
      case AppLifecycleState.detached:
        debugPrint('LiveStreamingScreen: App detached, cleaning up resources');
        _cleanupResources();
        break;
      case AppLifecycleState.hidden:
        debugPrint('LiveStreamingScreen: App hidden');
        break;
    }
  }

  Future<void> _initializeScreen() async {
    debugPrint('LiveStreamingScreen: Initializing screen');
    try {
      await _loadActiveStreams();
      _startPeriodicUpdates();
      debugPrint('LiveStreamingScreen: Screen initialized successfully');
    } catch (e) {
      debugPrint('LiveStreamingScreen: Error initializing screen: $e');
      if (mounted) {
        _showErrorSnackBar('Failed to initialize. Please restart the app.');
      }
    }
  }

  void _cleanupResources() {
    debugPrint('LiveStreamingScreen: Cleaning up resources');
    _chatTimer?.cancel();
    _streamsTimer?.cancel();
    _connectionHealthTimer?.cancel();
    _sessionIdController.dispose();
    _messageController.dispose();
    _chatScrollController.dispose();
    debugPrint('LiveStreamingScreen: Resources cleaned up');
  }

  void _pauseBackgroundOperations() {
    debugPrint('LiveStreamingScreen: Pausing background operations');
    _streamsTimer?.cancel();
    _chatTimer?.cancel();
    debugPrint('LiveStreamingScreen: Background operations paused');
  }

  void _resumeForegroundOperations() {
    debugPrint('LiveStreamingScreen: Resuming foreground operations');
    if (!_isDisposed && mounted) {
      _startPeriodicUpdates();
      if (isViewingStream) {
        _startChatPolling();
      }
      debugPrint('LiveStreamingScreen: Foreground operations resumed');
    }
  }

  void _startPeriodicUpdates() {
    debugPrint('LiveStreamingScreen: Starting periodic updates for active streams');
    _streamsTimer?.cancel();
    _streamsTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_isDisposed || !mounted || _isInBackground) {
        debugPrint('LiveStreamingScreen: Stopping periodic updates (disposed: $_isDisposed, mounted: $mounted, inBackground: $_isInBackground)');
        timer.cancel();
        return;
      }
      debugPrint('LiveStreamingScreen: Periodic update triggered');
      _loadActiveStreams();
    });
  }

  void _showErrorSnackBar(String message) {
    if (!mounted) return;

    debugPrint('LiveStreamingScreen: Showing error snackbar: $message');
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(color: Colors.white)),
        backgroundColor: Colors.red.shade700,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 4),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        action: SnackBarAction(
          label: 'Dismiss',
          textColor: Colors.white,
          onPressed: () {
            debugPrint('LiveStreamingScreen: Error snackbar dismissed');
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    if (!mounted) return;

    debugPrint('LiveStreamingScreen: Showing success snackbar: $message');
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(color: Colors.white)),
        backgroundColor: Colors.green.shade700,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  Future<Map<String, dynamic>?> _getUserData() async {
    debugPrint('LiveStreamingScreen: Fetching user data from SharedPreferences');
    try {
      final prefs = await SharedPreferences.getInstance();
      final userDataString = prefs.getString(AppConfig.userDataKey);

      if (userDataString != null) {
        final userData = json.decode(userDataString);
        debugPrint('LiveStreamingScreen: User data retrieved: $userData');
        return userData;
      } else {
        debugPrint('LiveStreamingScreen: No user data found in SharedPreferences');
        return null;
      }
    } catch (e) {
      debugPrint('LiveStreamingScreen: Error fetching user data: $e');
      return null;
    }
  }

  Future<void> _loadActiveStreams() async {
    if (!mounted || _isDisposed || _isInBackground) {
      debugPrint('LiveStreamingScreen: Skipping loadActiveStreams (mounted: $mounted, disposed: $_isDisposed, inBackground: $_isInBackground)');
      return;
    }

    debugPrint('LiveStreamingScreen: Loading active streams');
    setState(() {
      isLoading = true;
      debugPrint('LiveStreamingScreen: Set isLoading to true');
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        debugPrint('LiveStreamingScreen: Authentication token not found');
        throw Exception('Authentication token not found');
      }

      debugPrint('LiveStreamingScreen: Sending GET request to /api/live-streams/active with token: $token');
      final response = await http.get(
        Uri.parse('https://api.sasthra.com/api/live-streams/active'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      ).timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw TimeoutException('Request timeout'),
      );

      debugPrint('LiveStreamingScreen: Received response with status code: ${response.statusCode}');
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        debugPrint('LiveStreamingScreen: Active streams response: $data');
        if (mounted && !_isDisposed) {
          setState(() {
            activeStreams = List<Map<String, dynamic>>.from(data['streams'] ?? []);
            isLoading = false;
            _retryAttempts = 0;
            debugPrint('LiveStreamingScreen: Updated activeStreams with ${activeStreams.length} streams');
          });
        }
      } else {
        debugPrint('LiveStreamingScreen: Server error: ${response.statusCode}');
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('LiveStreamingScreen: Error loading active streams: $e');
      if (mounted && !_isDisposed) {
        setState(() {
          isLoading = false;
          debugPrint('LiveStreamingScreen: Set isLoading to false due to error');
        });

        if (_retryAttempts < _maxRetryAttempts &&
            (e is TimeoutException || e.toString().contains('network'))) {
          _retryAttempts++;
          debugPrint('LiveStreamingScreen: Retrying loadActiveStreams (attempt ${_retryAttempts}/$_maxRetryAttempts)');
          Timer(Duration(seconds: _retryAttempts * 2), () {
            if (mounted && !_isDisposed) {
              _loadActiveStreams();
            }
          });
        } else {
          _showErrorSnackBar('Error loading streams: ${_getErrorMessage(e)}');
        }
      }
    }
  }

  String _getErrorMessage(dynamic error) {
    debugPrint('LiveStreamingScreen: Generating error message for: $error');
    if (error is TimeoutException) {
      return 'Connection timeout. Please check your internet.';
    } else if (error.toString().contains('token')) {
      return 'Authentication failed. Please login again.';
    } else if (error.toString().contains('network') || error.toString().contains('connection')) {
      return 'Network error. Please check your connection.';
    } else {
      return 'Something went wrong. Please try again.';
    }
  }

  Future<void> _joinStreamBySessionId() async {
    final sessionId = _sessionIdController.text.trim();
    debugPrint('LiveStreamingScreen: Attempting to join stream with sessionId: $sessionId');
    if (sessionId.isEmpty) {
      debugPrint('LiveStreamingScreen: Session ID is empty');
      _showErrorSnackBar('Please enter a session ID');
      return;
    }

    if (isJoining || _isDisposed) {
      debugPrint('LiveStreamingScreen: Join operation skipped (isJoining: $isJoining, isDisposed: $_isDisposed)');
      return;
    }

    setState(() {
      isJoining = true;
      debugPrint('LiveStreamingScreen: Set isJoining to true');
    });

    try {
      // Enable full-screen mode and set landscape orientation
      await SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);

      final userData = await _getUserData();

      String userId;
      String userName;

      if (userData != null) {
        userId = userData['id']?.toString() ?? 'guest_${DateTime.now().millisecondsSinceEpoch}';
        userName = userData['name']?.toString() ?? userData['username']?.toString() ?? 'Guest User';
        debugPrint('LiveStreamingScreen: User data found: id=$userId, name=$userName');
      } else {
        userId = 'guest_${DateTime.now().millisecondsSinceEpoch}';
        userName = 'Guest User';
        debugPrint('LiveStreamingScreen: No user data, using guest: id=$userId, name=$userName');
      }

      const apiUrl = 'https://testing.sasthra.in/api/livekit/join';
      final requestBody = {
        'session_id': sessionId,
        'user_id': userId,
        'user_name': userName,
        'user_role': 'student',
      };
      debugPrint('LiveStreamingScreen: Sending POST request to $apiUrl with body: $requestBody');

      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode(requestBody),
      ).timeout(
        const Duration(seconds: 15),
        onTimeout: () => throw TimeoutException('Connection timeout'),
      );

      debugPrint('LiveStreamingScreen: Join response status: ${response.statusCode}');
      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        debugPrint('LiveStreamingScreen: Join response data: $responseData');

        final livekitToken = responseData['livekit_token'];
        final livekitUrl = responseData['livekit_url'];
        final participantId = responseData['participant_id'];
        final participantName = responseData['participant_name'];
        final isTeacher = responseData['is_teacher'] ?? false;
        final tokenExpiresIn = responseData['token_expires_in'];

        if (livekitToken == null || livekitUrl == null) {
          debugPrint('LiveStreamingScreen: Missing LiveKit token or URL in response');
          throw Exception('LiveKit token or URL not received from server');
        }

        if (mounted && !_isDisposed) {
          setState(() {
            isJoining = false;
            isViewingStream = true;
            isChatOpen = true; // Start with chat open
            currentStream = {
              'id': sessionId,
              'title': 'Live Session: $sessionId',
              'instructor': participantName ?? userName,
              'viewers': 1,
              'livekit_token': livekitToken,
              'livekit_url': livekitUrl,
              'participant_id': participantId,
              'participant_name': participantName,
              'user_id': userId,
              'user_name': userName,
              'is_teacher': isTeacher,
              'token_expires_in': tokenExpiresIn,
              'session_id': sessionId,
            };
            debugPrint('LiveStreamingScreen: Updated state with currentStream: $currentStream');
          });

          _showSuccessSnackBar('Successfully joined session: $sessionId');
          debugPrint('LiveStreamingScreen: Successfully joined session: $sessionId');
          _startChatPolling();
          _sessionIdController.clear();
          debugPrint('LiveStreamingScreen: Cleared session ID controller');
        }
      } else {
        String errorMessage = 'Failed to join session';
        try {
          final errorData = json.decode(response.body);
          errorMessage = errorData['message'] ?? errorData['error'] ?? errorMessage;
        } catch (e) {
          errorMessage = 'Server returned status ${response.statusCode}';
        }
        debugPrint('LiveStreamingScreen: Join failed with error: $errorMessage');
        throw Exception('API Error (${response.statusCode}): $errorMessage');
      }
    } catch (e) {
      debugPrint('LiveStreamingScreen: Error joining stream: $e');
      if (mounted && !_isDisposed) {
        setState(() {
          isJoining = false;
          debugPrint('LiveStreamingScreen: Set isJoining to false due to error');
        });
        // Reset to default UI mode and portrait orientation on error
        await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
        await SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
          DeviceOrientation.portraitDown,
        ]);
        _showErrorSnackBar('Failed to join session: ${_getErrorMessage(e)}');
      }
    }
  }

  Future<void> _startChatPolling() async {
    debugPrint('LiveStreamingScreen: Starting chat polling');
    _chatTimer?.cancel();
    _chatTimer = Timer.periodic(const Duration(seconds: 5), (timer) async {
      if (!mounted || !isViewingStream || _isDisposed || _isInBackground) {
        debugPrint('LiveStreamingScreen: Stopping chat polling (mounted: $mounted, isViewingStream: $isViewingStream, disposed: $_isDisposed, inBackground: $_isInBackground)');
        timer.cancel();
        return;
      }

      try {
        final sessionId = currentStream?['session_id'];
        if (sessionId == null) {
          debugPrint('LiveStreamingScreen: No session ID available for chat polling');
          _showErrorSnackBar('No session ID available for chat');
          return;
        }

        debugPrint('LiveStreamingScreen: Polling chat history for session: $sessionId');
        final response = await http.get(
          Uri.parse('https://testing.sasthra.in/api/chat/history/$sessionId'),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ).timeout(const Duration(seconds: 10));

        debugPrint('LiveStreamingScreen: Chat polling response status: ${response.statusCode}, body: ${response.body}');
        if (response.statusCode == 200) {
          final data = json.decode(response.body);
          final List<dynamic> messages = data is List ? data : data['messages'] ?? [];
          debugPrint('LiveStreamingScreen: Parsed messages: $messages');
          if (mounted && !_isDisposed) {
            debugPrint('LiveStreamingScreen: Calling setState for chatMessages update');
            setState(() {
              chatMessages = List<Map<String, dynamic>>.from(messages);
              debugPrint('LiveStreamingScreen: chatMessages updated: $chatMessages');
              if (!isChatOpen) {
                unreadMessages = chatMessages.length;
              }
              debugPrint('LiveStreamingScreen: Updated chatMessages with ${chatMessages.length} messages, unread: $unreadMessages');
            });
            _scrollToBottom();
          }
        } else {
          debugPrint('LiveStreamingScreen: Chat polling failed with status: ${response.statusCode}, body: ${response.body}');
          _showErrorSnackBar('Failed to fetch chat messages: Server error ${response.statusCode}');
        }
      } catch (e) {
        debugPrint('LiveStreamingScreen: Error polling chat messages: $e');
        if (mounted && !_isDisposed) {
          _showErrorSnackBar('Failed to fetch chat messages: ${_getErrorMessage(e)}');
        }
      }
    });
  }

  Future<void> _sendChatMessage() async {
    final message = _messageController.text.trim();
    debugPrint('LiveStreamingScreen: Attempting to send message: $message');
    if (message.isEmpty) {
      debugPrint('LiveStreamingScreen: Empty message, skipping send');
      _showErrorSnackBar('Please enter a message');
      return;
    }

    if (isSendingMessage || _isDisposed || !isViewingStream) {
      debugPrint('LiveStreamingScreen: Send message skipped (isSendingMessage: $isSendingMessage, isDisposed: $_isDisposed, isViewingStream: $isViewingStream)');
      return;
    }

    setState(() {
      isSendingMessage = true;
      debugPrint('LiveStreamingScreen: Set isSendingMessage to true');
    });

    try {
      final userData = await _getUserData();
      final sessionId = currentStream?['session_id'];
      if (sessionId == null) {
        debugPrint('LiveStreamingScreen: No session ID available for sending message');
        throw Exception('No active session');
      }

      String userId = userData?['id']?.toString() ?? 'guest_${DateTime.now().millisecondsSinceEpoch}';
      String userName = userData?['name']?.toString() ?? userData?['username']?.toString() ?? 'Guest User';

      final requestBody = {
        'session_id': sessionId,
        'message': message,
        'sender_id': userId,
        'sender_name': userName,
        'timestamp': DateTime.now().toUtc().toIso8601String(),
      };

      debugPrint('LiveStreamingScreen: Sending POST request to chat endpoint with body: $requestBody');
      final response = await http.post(
        Uri.parse('https://testing.sasthra.in/api/chat/send'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode(requestBody),
      ).timeout(const Duration(seconds: 10));

      debugPrint('LiveStreamingScreen: Chat send response status: ${response.statusCode}, body: ${response.body}');
      if (response.statusCode == 200) {
        if (mounted && !_isDisposed) {
          setState(() {
            isSendingMessage = false;
            _messageController.clear();
            debugPrint('LiveStreamingScreen: Message sent successfully, cleared message controller');
          });
          _showSuccessSnackBar('Message sent');
          await _startChatPolling();
          _scrollToBottom();
        }
      } else {
        debugPrint('LiveStreamingScreen: Failed to send message, status: ${response.statusCode}, body: ${response.body}');
        throw Exception('Failed to send message: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('LiveStreamingScreen: Error sending message: $e');
      if (mounted && !_isDisposed) {
        setState(() {
          isSendingMessage = false;
          debugPrint('LiveStreamingScreen: Set isSendingMessage to false due to error');
        });
        _showErrorSnackBar('Failed to send message: ${_getErrorMessage(e)}');
      }
    }
  }

  void _scrollToBottom() {
    if (_chatScrollController.hasClients) {
      debugPrint('LiveStreamingScreen: Scrolling to bottom, maxScrollExtent: ${_chatScrollController.position.maxScrollExtent}');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _chatScrollController.animateTo(
          _chatScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
        debugPrint('LiveStreamingScreen: Scrolled to bottom');
      });
    } else {
      debugPrint('LiveStreamingScreen: ScrollController has no clients');
      Timer(const Duration(milliseconds: 100), _scrollToBottom);
    }
  }

  void _showSessionInfo() {
    debugPrint('LiveStreamingScreen: Showing session info dialog');
    final stream = currentStream;
    if (stream == null) {
      debugPrint('LiveStreamingScreen: No current stream to show info for');
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: const Text('Session Information', style: TextStyle(fontWeight: FontWeight.bold)),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildInfoRow('Session ID', stream['session_id'] ?? 'Unknown'),
                _buildInfoRow('Participant ID', stream['participant_id'] ?? 'Unknown'),
                _buildInfoRow('Participant Name', stream['participant_name'] ?? 'Unknown'),
                _buildInfoRow('User Role', stream['is_teacher'] == true ? 'Teacher' : 'Student'),
                _buildInfoRow('LiveKit URL', stream['livekit_url'] ?? 'Not available'),
                _buildInfoRow('Token Expires In', '${stream['token_expires_in'] ?? 0} seconds'),
                _buildInfoRow('Connection Status', 'Connected'),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                debugPrint('LiveStreamingScreen: Copying session info');
                _copySessionInfo(stream);
              },
              child: const Text('Copy Info', style: TextStyle(color: Colors.blue)),
            ),
            TextButton(
              onPressed: () {
                debugPrint('LiveStreamingScreen: Closing session info dialog');
                Navigator.of(context).pop();
              },
              child: const Text('Close', style: TextStyle(color: Colors.blue)),
            ),
          ],
        );
      },
    );
  }

  Widget _buildInfoRow(String label, String value) {
    debugPrint('LiveStreamingScreen: Building info row for $label: $value');
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14, color: Colors.black54),
            ),
          ),
        ],
      ),
    );
  }

  void _copySessionInfo(Map<String, dynamic> stream) {
    debugPrint('LiveStreamingScreen: Copying session info to clipboard');
    final sessionInfo = '''
Session Information:
- Session ID: ${stream['session_id'] ?? 'Unknown'}
- Participant ID: ${stream['participant_id'] ?? 'Unknown'}
- Participant Name: ${stream['participant_name'] ?? 'Unknown'}
- User Role: ${stream['is_teacher'] == true ? 'Teacher' : 'Student'}
- LiveKit URL: ${stream['livekit_url'] ?? 'Not available'}
- Token Expires In: ${stream['token_expires_in'] ?? 0} seconds
- Connection Status: Connected
''';

    Clipboard.setData(ClipboardData(text: sessionInfo));
    debugPrint('LiveStreamingScreen: Session info copied to clipboard');

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Session information copied to clipboard', style: TextStyle(color: Colors.white)),
        backgroundColor: Colors.blue.shade700,
        duration: const Duration(seconds: 2),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  void _leaveStream() async {
    debugPrint('LiveStreamingScreen: Leaving stream');
    if (!mounted || _isDisposed) {
      debugPrint('LiveStreamingScreen: Leave stream skipped (mounted: $mounted, disposed: $_isDisposed)');
      return;
    }

    // Reset to default UI mode and portrait orientation
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    _chatTimer?.cancel();
    _connectionHealthTimer?.cancel();
    debugPrint('LiveStreamingScreen: Timers cancelled');

    setState(() {
      isViewingStream = false;
      currentStream = null;
      isChatOpen = false;
      chatMessages.clear();
      unreadMessages = 0;
      debugPrint('LiveStreamingScreen: Cleared stream state');
    });

    _startPeriodicUpdates();
    _showSuccessSnackBar('Left the session successfully');
    debugPrint('LiveStreamingScreen: Successfully left the session');
  }

  void _toggleChat() {
    debugPrint('LiveStreamingScreen: Toggling chat, current isChatOpen: $isChatOpen');
    setState(() {
      isChatOpen = !isChatOpen;
      debugPrint('LiveStreamingScreen: isChatOpen set to $isChatOpen');
      if (isChatOpen) {
        unreadMessages = 0;
        _scrollToBottom();
      }
    });
  }

  Widget _buildStreamViewer() {
    debugPrint('LiveStreamingScreen: Building stream viewer, currentStream: $currentStream');
    final stream = currentStream;
    final sessionId = stream?['session_id'] ?? 'Unknown';
    final participantName = stream?['participant_name'] ?? stream?['user_name'] ?? 'Unknown';
    final isTeacher = stream?['is_teacher'] ?? false;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          currentStream?['title'] ?? 'Live Stream',
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        backgroundColor: Colors.blue.shade800,
        elevation: 2,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: _leaveStream,
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline, color: Colors.white),
            onPressed: () {
              debugPrint('LiveStreamingScreen: Session info button pressed');
              _showSessionInfo();
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          Row(
            children: [
              Expanded(
                child: Container(
                  color: Colors.black,
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      LiveKitVideoWidget(
                        livekitUrl: stream?['livekit_url'] ?? '',
                        livekitToken: stream?['livekit_token'] ?? '',
                        participantName: participantName,
                        isTeacher: isTeacher,
                      ),
                      Positioned(
                        top: 16,
                        left: 16,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.green.shade600.withOpacity(0.9),
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                width: 10,
                                height: 10,
                                decoration: const BoxDecoration(
                                  color: Colors.white,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                'Live',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Positioned(
                        top: 16,
                        right: 16,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.7),
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Text(
                            'Session: ${sessionId.length > 8 ? '${sessionId.substring(0, 8)}...' : sessionId}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              if (isChatOpen)
                Flexible(
                  flex: 2,
                  child: Container(
                    constraints: const BoxConstraints(minWidth: 250, maxWidth: 500),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(left: BorderSide(color: Colors.grey.shade300)),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          offset: const Offset(-2, 0),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade50,
                            border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.chat, color: Colors.blue.shade700),
                              const SizedBox(width: 8),
                              Text(
                                'Live Chat',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.blue.shade900,
                                ),
                              ),
                              const Spacer(),
                              IconButton(
                                icon: Icon(Icons.refresh, color: Colors.blue.shade700),
                                onPressed: () {
                                  debugPrint('LiveStreamingScreen: Manual chat refresh triggered');
                                  _startChatPolling();
                                },
                                tooltip: 'Refresh Chat',
                              ),
                              IconButton(
                                icon: Icon(Icons.close, color: Colors.blue.shade700),
                                onPressed: _toggleChat,
                                tooltip: 'Hide Chat',
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: chatMessages.isEmpty
                              ? Center(
                                  child: Text(
                                    'No messages yet',
                                    style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
                                  ),
                                )
                              : ListView.builder(
                                  controller: _chatScrollController,
                                  padding: const EdgeInsets.all(12),
                                  itemCount: chatMessages.length,
                                  itemBuilder: (context, index) {
                                    debugPrint('LiveStreamingScreen: Building message $index: ${chatMessages[index]}');
                                    final message = chatMessages[index];
                                    final isCurrentUser =
                                        message['sender_id'] == currentStream?['user_id'];
                                    return Padding(
                                      padding: const EdgeInsets.symmetric(vertical: 6),
                                      child: Align(
                                        alignment: isCurrentUser
                                            ? Alignment.centerRight
                                            : Alignment.centerLeft,
                                        child: Container(
                                          constraints: BoxConstraints(
                                            maxWidth: MediaQuery.of(context).size.width * 0.7,
                                          ),
                                          padding: const EdgeInsets.all(12),
                                          margin: const EdgeInsets.symmetric(horizontal: 8),
                                          decoration: BoxDecoration(
                                            color: isCurrentUser
                                                ? Colors.blue.shade100
                                                : Colors.grey.shade100,
                                            borderRadius: BorderRadius.circular(12),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black.withOpacity(0.1),
                                                blurRadius: 4,
                                                offset: const Offset(0, 2),
                                              ),
                                            ],
                                          ),
                                          child: Column(
                                            crossAxisAlignment: isCurrentUser
                                                ? CrossAxisAlignment.end
                                                : CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                message['sender_name'] ?? 'Unknown',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w600,
                                                  fontSize: 14,
                                                  color: isCurrentUser
                                                      ? Colors.blue.shade900
                                                      : Colors.black87,
                                                ),
                                              ),
                                              const SizedBox(height: 6),
                                              Text(
                                                message['message'] ?? '',
                                                style: const TextStyle(fontSize: 16),
                                              ),
                                              const SizedBox(height: 6),
                                              Text(
                                                message['timestamp'] != null
                                                    ? DateTime.parse(message['timestamp'])
                                                        .toLocal()
                                                        .toString()
                                                        .substring(11, 16)
                                                    : '',
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.grey.shade600,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                        ),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border(top: BorderSide(color: Colors.grey.shade300)),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: TextField(
                                  controller: _messageController,
                                  decoration: InputDecoration(
                                    hintText: 'Type a message...',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide(color: Colors.grey.shade300),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide(color: Colors.grey.shade300),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide(color: Colors.blue.shade700),
                                    ),
                                    filled: true,
                                    fillColor: Colors.grey.shade50,
                                  ),
                                  enabled: !isSendingMessage,
                                  onSubmitted: (_) => _sendChatMessage(),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Material(
                                color: isSendingMessage ? Colors.grey.shade300 : Colors.blue.shade700,
                                borderRadius: BorderRadius.circular(12),
                                child: InkWell(
                                  borderRadius: BorderRadius.circular(12),
                                  onTap: isSendingMessage ? null : _sendChatMessage,
                                  child: Container(
                                    padding: const EdgeInsets.all(12),
                                    child: isSendingMessage
                                        ? const SizedBox(
                                            width: 24,
                                            height: 24,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor: AlwaysStoppedAnimation(Colors.white),
                                            ),
                                          )
                                        : const Icon(Icons.send, color: Colors.white, size: 24),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
          if (!isChatOpen)
            Positioned(
              bottom: 16,
              right: 16,
              child: Stack(
                children: [
                  FloatingActionButton(
                    onPressed: _toggleChat,
                    backgroundColor: Colors.blue.shade700,
                    child: const Icon(Icons.chat, color: Colors.white),
                    tooltip: 'Show Chat',
                  ),
                  if (unreadMessages > 0)
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.red.shade600,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 20,
                          minHeight: 20,
                        ),
                        child: Text(
                          '$unreadMessages',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    debugPrint('LiveStreamingScreen: Building UI (isViewingStream: $isViewingStream)');

    if (isViewingStream) {
      debugPrint('LiveStreamingScreen: Rendering stream viewer');
      return _buildStreamViewer();
    }

    debugPrint('LiveStreamingScreen: Rendering main screen with active streams');
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Live Streaming',
          style: TextStyle(fontWeight: FontWeight.w600, color: Colors.white),
        ),
        backgroundColor: Colors.blue.shade800,
        elevation: 2,
      ),
      body: RefreshIndicator(
        onRefresh: () {
          debugPrint('LiveStreamingScreen: RefreshIndicator triggered');
          return _loadActiveStreams();
        },
        color: Colors.blue.shade700,
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Join Stream Manually',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: Colors.blue.shade900,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _sessionIdController,
                            decoration: InputDecoration(
                              labelText: 'Session ID',
                              hintText: 'Enter session ID',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(color: Colors.grey.shade300),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(color: Colors.grey.shade300),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(color: Colors.blue.shade700),
                              ),
                              filled: true,
                              fillColor: Colors.grey.shade50,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Material(
                          color: isJoining ? Colors.grey.shade300 : Colors.blue.shade700,
                          borderRadius: BorderRadius.circular(12),
                          child: InkWell(
                            borderRadius: BorderRadius.circular(12),
                            onTap: isJoining ? null : _joinStreamBySessionId,
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                              child: isJoining
                                  ? const SizedBox(
                                      width: 24,
                                      height: 24,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation(Colors.white),
                                      ),
                                    )
                                  : const Text(
                                      'Join',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            SliverPadding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              sliver: SliverToBoxAdapter(
                child: Text(
                  'Active Streams',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue.shade900,
                  ),
                ),
              ),
            ),
            if (isLoading)
              const SliverFillRemaining(
                child: Center(child: CircularProgressIndicator()),
              )
            else if (activeStreams.isEmpty)
              SliverFillRemaining(
                child: Center(
                  child: Text(
                    'No active streams available',
                    style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
                  ),
                ),
              )
            else
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final stream = activeStreams[index];
                      debugPrint('LiveStreamingScreen: Building stream card for index $index: ${stream['title']}');
                      return Card(
                        elevation: 4,
                        margin: const EdgeInsets.only(bottom: 16),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(12),
                          onTap: () async {
                            debugPrint('LiveStreamingScreen: Stream tapped: ${stream['title']}');
                            // Enable full-screen mode and set landscape orientation
                            await SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
                            await SystemChrome.setPreferredOrientations([
                              DeviceOrientation.landscapeLeft,
                              DeviceOrientation.landscapeRight,
                            ]);
                            setState(() {
                              isViewingStream = true;
                              currentStream = stream;
                              debugPrint('LiveStreamingScreen: Set isViewingStream to true, currentStream updated');
                            });
                          },
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Row(
                              children: [
                                CircleAvatar(
                                  backgroundColor: Colors.red.shade600,
                                  radius: 24,
                                  child: const Icon(Icons.live_tv, color: Colors.white, size: 28),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        stream['title'] ?? 'Untitled Stream',
                                        style: const TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: Colors.black87,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'Instructor: ${stream['instructor'] ?? 'Unknown'}',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.grey.shade600,
                                        ),
                                      ),
                                      Text(
                                        'Viewers: ${stream['viewers'] ?? 0}',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.grey.shade600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Icon(Icons.arrow_forward_ios, color: Colors.grey.shade400),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                    childCount: activeStreams.length,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}