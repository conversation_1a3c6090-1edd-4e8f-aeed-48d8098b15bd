import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/config/app_config.dart';
import '../controller/problem_solver_controller.dart';
import '../model/search_result_models.dart';
import 'package:flutter_math_fork/flutter_math.dart';

class ProblemSolverPage extends StatefulWidget {
  const ProblemSolverPage({super.key});

  @override
  State<ProblemSolverPage> createState() => _ProblemSolverPageState();
}

class _ProblemSolverPageState extends State<ProblemSolverPage> with TickerProviderStateMixin {
  final TextEditingController _textController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _textController.dispose();
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ProblemSolverController(),
      child: Scaffold(
        backgroundColor: const Color(0xFFF8FAFC), // Modern neutral background
        appBar: _buildModernAppBar(),
        body: Consumer<ProblemSolverController>(
          builder: (context, controller, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: Column(
                children: [
                  // Enhanced Settings Panel
                  _buildEnhancedSettingsPanel(controller),
                  
                  // Main Content Area with improved design
                  Expanded(
                    child: _buildMainContent(controller),
                  ),
                  
                  // Enhanced Input Panel
                  _buildEnhancedInputPanel(controller),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  PreferredSizeWidget _buildModernAppBar() {
    return AppBar(
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(Icons.psychology, size: 24, color: Colors.white),
          ),
          const SizedBox(width: 12),
          const Text(
            'AI Problem Solver',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 20,
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
      backgroundColor: const Color(0xFF6366F1), // Modern indigo color
      foregroundColor: Colors.white,
      elevation: 0,
      flexibleSpace: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedSettingsPanel(ProblemSolverController controller) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Settings Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF6366F1).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(Icons.tune, color: Color(0xFF6366F1), size: 20),
              ),
              const SizedBox(width: 12),
              const Text(
                'Settings',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // Mode and Language Selection with improved design
          Row(
            children: [
              Expanded(
                child: _buildModernDropdown(
                  controller, 
                  'Mode', 
                  controller.selectedMode,
                  controller.modes,
                  Icons.psychology,
                  const Color(0xFF6366F1),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildModernDropdown(
                  controller, 
                  'Language', 
                  controller.selectedLanguage,
                  controller.languages,
                  Icons.language,
                  const Color(0xFF059669),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          
          // Enhanced Action Buttons
          _buildModernActionButtons(controller),
        ],
      ),
    );
  }

  Widget _buildModernDropdown(
    ProblemSolverController controller,
    String type,
    String currentValue,
    List<String> items,
    IconData icon,
    Color color,
  ) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.2)),
        color: color.withOpacity(0.05),
      ),
      child: DropdownButtonFormField<String>(
        value: currentValue,
        decoration: InputDecoration(
          labelText: type,
          labelStyle: TextStyle(
            color: color,
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
          prefixIcon: Container(
            margin: const EdgeInsets.all(8),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
        items: items.map<DropdownMenuItem<String>>((String value) {
          return DropdownMenuItem<String>(
            value: value,
            child: Text(
              value.toUpperCase(),
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          );
        }).toList(),
        onChanged: (String? newValue) {
          if (type == 'Mode') {
            controller.setMode(newValue);
          } else {
            controller.setLanguage(newValue);
          }
        },
      ),
    );
  }

  Widget _buildModernActionButtons(ProblemSolverController controller) {
    return Row(
      children: [
        // Expanded(
        //   flex: 2,
        //   child: _buildModernActionButton(
        //     'Solve Doubt',
        //     Icons.psychology,
        //     const LinearGradient(colors: [Color(0xFF8B5CF6), Color(0xFF7C3AED)]),
        //     () => _handleDoubtSolver(controller),
        //     controller.isLoading && controller.lastSearchType == SearchType.doubtSolver,
        //   ),
        // ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildModernActionButton(
            'YouTube',
            Icons.play_circle_filled,
            const LinearGradient(colors: [Color(0xFFEF4444), Color(0xFFDC2626)]),
            () => _handleSearch(controller, SearchType.youtubeSearch),
            controller.isLoading && controller.lastSearchType == SearchType.youtubeSearch,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildModernActionButton(
            'Web',
            Icons.search,
            const LinearGradient(colors: [Color(0xFF059669), Color(0xFF047857)]),
            () => _handleSearch(controller, SearchType.webSearch),
            controller.isLoading && controller.lastSearchType == SearchType.webSearch,
          ),
        ),
      ],
    );
  }

  Widget _buildModernActionButton(
    String label, 
    IconData icon, 
    Gradient gradient, 
    VoidCallback onPressed, 
    bool isLoading
  ) {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        gradient: gradient,
        borderRadius: BorderRadius.circular(14),
        boxShadow: [
          BoxShadow(
            color: gradient.colors.first.withOpacity(0.3),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton.icon(
        onPressed: isLoading ? null : onPressed,
        icon: isLoading 
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
            )
          : Icon(icon, size: 18),
        label: Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
        ),
      ),
    );
  }

  Widget _buildMainContent(ProblemSolverController controller) {
    if (controller.lastSearchType == SearchType.doubtSolver) {
      return _buildChatInterface(controller);
    } else if (controller.lastSearchType == SearchType.youtubeSearch) {
      return _buildYoutubeResults(controller);
    } else if (controller.lastSearchType == SearchType.webSearch) {
      return _buildWebResults(controller);
    } else {
      return _buildWelcomeScreen();
    }
  }

  Widget _buildWelcomeScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
              ),
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF6366F1).withOpacity(0.3),
                  spreadRadius: 0,
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: const Icon(Icons.psychology, size: 64, color: Colors.white),
          ),
          const SizedBox(height: 24),
          const Text(
            'Welcome to AI Problem Solver',
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.w700,
              color: Color(0xFF1F2937),
              letterSpacing: -0.5,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Enter your question below and choose an action',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 32),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            decoration: BoxDecoration(
              color: const Color(0xFF6366F1).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              '✨ Ask anything, get instant help',
              style: TextStyle(
                color: Color(0xFF6366F1),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatInterface(ProblemSolverController controller) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Enhanced Chat Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF8B5CF6), Color(0xFF7C3AED)],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Icon(Icons.psychology, color: Colors.white, size: 20),
                ),
                const SizedBox(width: 12),
                const Text(
                  'AI Tutor Chat',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${controller.selectedMode.toUpperCase()} • ${controller.selectedLanguage.toUpperCase()}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Chat Messages
          Expanded(
            child: _buildChatMessages(controller),
          ),
        ],
      ),
    );
  }

  Widget _buildChatMessages(ProblemSolverController controller) {
    List<Widget> messages = [];
    
    // Add user message if there's a recent query
    if (controller.doubtText.isNotEmpty || controller.imageFile != null || controller.audioPath != null) {
      messages.add(_buildUserMessage(controller));
    }
    
    // Add AI response if available
    if (controller.response.isNotEmpty) {
      messages.add(_buildAIMessage(controller.response));
    }
    
    // Add history messages
    if (controller.history != null && controller.history!.isNotEmpty) {
      for (var msg in controller.history!) {
        if (msg['role'] == 'user') {
          messages.add(_buildHistoryUserMessage(msg['content']));
        } else if (msg['role'] == 'assistant') {
          messages.add(_buildHistoryAIMessage(msg['content']));
        }
      }
    }
    
    // Show error if any
    if (controller.error.isNotEmpty) {
      messages.add(_buildErrorMessage(controller.error));
    }
    
    if (messages.isEmpty) {
      return Center(
        child: Text(
          'Start a conversation with the AI tutor',
          style: TextStyle(color: Colors.grey[500], fontSize: 16),
        ),
      );
    }
    
    WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToBottom());
    
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: messages.length,
      itemBuilder: (context, index) => messages[index],
    );
  }

  Widget _buildUserMessage(ProblemSolverController controller) {
    return Align(
      alignment: Alignment.centerRight,
      child: Container(
        margin: const EdgeInsets.only(bottom: 16, left: 50),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
          ),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
            bottomLeft: Radius.circular(20),
            bottomRight: Radius.circular(4),
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF6366F1).withOpacity(0.3),
              spreadRadius: 0,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (controller.doubtText.isNotEmpty)
              Text(
                controller.doubtText,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  height: 1.4,
                ),
              ),
            if (controller.imageFile != null) ...[
              const SizedBox(height: 12),
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.file(
                  controller.imageFile!,
                  height: 120,
                  width: 180,
                  fit: BoxFit.cover,
                ),
              ),
            ],
            if (controller.audioPath != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.audiotrack, color: Colors.white, size: 18),
                    SizedBox(width: 8),
                    Text('Audio message', style: TextStyle(color: Colors.white, fontSize: 14)),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

 String _preprocessResponse(String response) {
    String cleaned = response
        .replaceAll(RegExp(r'\$\s+\$'), r'$$') // Fix "$ $" to "$$"
        .replaceAll(RegExp(r'\r\n|\r'), '\n') // Standardize newlines
        .replaceAllMapped(RegExp(r'(\${1,2})([\s\S]*?)\1'), (match) {
          return match.group(0)!.replaceAll('\n', ' ');
        }).trim();
    return cleaned;
  }


  Widget _buildAIMessage(String message) {
    final processedText = _preprocessResponse(message);
    if (processedText.isEmpty) {
      return const Text(
        'Received an empty response.',
        style: TextStyle(color: Colors.grey, fontSize: 16),
      );
    }

    // Split the response into paragraphs
    final paragraphs = processedText.split(RegExp(r'\n\s*\n+'));

    List<Widget> contentWidgets = [];
    for (final paragraph in paragraphs) {
      if (paragraph.trim().isEmpty) continue;
      contentWidgets.add(_buildParagraphWidget(paragraph.trim()));
    }

    if (contentWidgets.isEmpty) {
      return const Text(
        'No content to display',
        style: TextStyle(color: Colors.grey, fontSize: 16),
      );
    }

    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.only(bottom: 16, right: 50),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFFF8FAFC),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
            bottomLeft: Radius.circular(4),
            bottomRight: Radius.circular(20),
          ),
          border: Border.all(color: const Color(0xFFE5E7EB)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.psychology, size: 16, color: Colors.white),
                ),
                const SizedBox(width: 8),
                const Text(
                  'AI Tutor',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF6366F1),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: contentWidgets,
            ),
          ],
        ),
      ),
    );
  }



Widget _buildParagraphWidget(String paragraph) {
    List<Widget> inlineWidgets = [];
    final RegExp latexRegex = RegExp(r'(\$\$.*?\$\$)|(\$.*?\$)');
    int lastMatchEnd = 0;

    for (final match in latexRegex.allMatches(paragraph)) {
      // Add plain text before the LaTeX match
      if (match.start > lastMatchEnd) {
        inlineWidgets.add(Text(
          paragraph.substring(lastMatchEnd, match.start),
          style: const TextStyle(color: Color(0xFF374151), fontSize: 16, height: 1.5),
        ));
      }

      // Process LaTeX
      final latexText = match.group(0)!;
      final bool isDisplayMode = latexText.startsWith(r'$$');
      final String cleanedLatex = latexText
          .substring(isDisplayMode ? 2 : 1, latexText.length - (isDisplayMode ? 2 : 1))
          .trim();

      if (cleanedLatex.isNotEmpty) {
        inlineWidgets.add(
          Math.tex(
            cleanedLatex,
            mathStyle: isDisplayMode ? MathStyle.display : MathStyle.text,
            textStyle: const TextStyle(fontSize: 16),
            onErrorFallback: (err) {
              print("LaTeX Rendering Error: ${err.message} for \"$cleanedLatex\"");
              return Text(
                '[$cleanedLatex]',
                style: const TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
              );
            },
          ),
        );
      }
      lastMatchEnd = match.end;
    }

    // Add remaining plain text
    if (lastMatchEnd < paragraph.length) {
      inlineWidgets.add(Text(
        paragraph.substring(lastMatchEnd),
        style: const TextStyle(color: Color(0xFF374151), fontSize: 16, height: 1.5),
      ));
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Wrap(
        crossAxisAlignment: WrapCrossAlignment.center,
        runSpacing: 8.0,
        spacing: 4.0,
        children: inlineWidgets,
      ),
    );
  }


  Widget _buildHistoryUserMessage(String message) {
    return Align(
      alignment: Alignment.centerRight,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8, left: 50),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.deepPurple.withOpacity(0.7),
          borderRadius: BorderRadius.circular(15),
        ),
        child: Text(
          message,
          style: const TextStyle(color: Colors.white, fontSize: 14),
        ),
      ),
    );
  }

  Widget _buildHistoryAIMessage(String message) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8, right: 50),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(15),
        ),
        child: Text(
          message,
          style: const TextStyle(fontSize: 14, height: 1.3),
        ),
      ),
    );
  }

  Widget _buildErrorMessage(String error) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red[200]!),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red[600]),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              error,
              style: TextStyle(color: Colors.red[600], fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildYoutubeResults(ProblemSolverController controller) {
    if (controller.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    
    if (controller.youtubeResults.isEmpty) {
      return _buildEmptyState('No YouTube videos found', Icons.video_library);
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Results Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.play_circle_filled, color: Colors.red),
                const SizedBox(width: 8),
                Text(
                  'YouTube Results',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
                const Spacer(),
                Text(
                  '${controller.youtubeResults.length} videos',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.red.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          
          // Results List
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: controller.youtubeResults.length,
              itemBuilder: (context, index) {
                final result = controller.youtubeResults[index];
                return _buildYoutubeCard(result, controller);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildYoutubeCard(YoutubeSearchResult result, ProblemSolverController controller) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => controller.launchURL('https://www.youtube.com/watch?v=${result.videoId}'),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Thumbnail
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: result.thumbnailUrl.isNotEmpty
                    ? Image.network(
                        result.thumbnailUrl,
                        width: 120,
                        height: 90,
                        fit: BoxFit.cover,
                        errorBuilder: (c, o, s) => Container(
                          width: 120,
                          height: 90,
                          color: Colors.grey[300],
                          child: Icon(Icons.video_library, size: 40, color: Colors.grey[600]),
                        ),
                      )
                    : Container(
                        width: 120,
                        height: 90,
                        color: Colors.grey[300],
                        child: Icon(Icons.video_library, size: 40, color: Colors.grey[600]),
                      ),
              ),
              const SizedBox(width: 12),
              
              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      result.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      result.channelTitle,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(Icons.play_arrow, size: 16, color: Colors.red),
                        const SizedBox(width: 4),
                        Text(
                          'Watch on YouTube',
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWebResults(ProblemSolverController controller) {
    if (controller.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    
    if (controller.webResults.isEmpty) {
      return _buildEmptyState('No web results found', Icons.search);
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Results Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.search, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Web Results',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                const Spacer(),
                Text(
                  '${controller.webResults.length} results',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.green.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          
          // Results List
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: controller.webResults.length,
              itemBuilder: (context, index) {
                final result = controller.webResults[index];
                return _buildWebCard(result, controller);
              },
            ),
          ),
        ],
      ),
    );
  }

 Widget _buildWebCard(WebSearchResult result, ProblemSolverController controller) {
  return Card(
    elevation: 2,
    margin: const EdgeInsets.only(bottom: 12),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    child: InkWell(
      onTap: () async {
        try {
          await controller.launchURL(result.link);
        } catch (e) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to open link: $e'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      },
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              result.title,
              style: const TextStyle(
                color: Colors.blue,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Text(
              result.snippet,
              style: TextStyle(
                color: Colors.grey[700],
                fontSize: 14,
                height: 1.4,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.link, size: 16, color: Colors.green),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    result.link,
                    style: TextStyle(
                      color: Colors.green,
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    ),
  );
}

  Widget _buildEmptyState(String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedInputPanel(ProblemSolverController controller) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Attachments Preview
          if (controller.imageFile != null || controller.audioPath != null)
            _buildEnhancedAttachmentsPreview(controller),
          
          // Enhanced Text Input
          Row(
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFFF8FAFC),
                    borderRadius: BorderRadius.circular(24),
                    border: Border.all(color: const Color(0xFFE5E7EB)),
                  ),
                  child: TextField(
                    controller: _textController,
                    onChanged: controller.setDoubtText,
                    decoration: const InputDecoration(
                      hintText: 'Ask me anything...',
                      hintStyle: TextStyle(
                        color: Color(0xFF9CA3AF),
                        fontWeight: FontWeight.w400,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    ),
                    maxLines: null,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Color(0xFF374151),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              
              // Enhanced Attachment Buttons
              _buildModernAttachmentButton(
                Icons.image,
                const Color(0xFF3B82F6),
                () => _showImageChoiceDialog(context, controller),
              ),
              const SizedBox(width: 8),
              _buildModernAttachmentButton(
                controller.isRecording ? Icons.stop : Icons.mic,
                controller.isRecording ? const Color(0xFFEF4444) : const Color(0xFFF59E0B),
                controller.toggleAudioRecording,
              ),
              const SizedBox(width: 8),
              _buildModernAttachmentButton(
                Icons.send,
                const Color(0xFF10B981),
                () => _handleDoubtSolver(controller))
            ],
          ),
        ],
      ),
    );
  }




  Widget _buildModernAttachmentButton(IconData icon, Color color, VoidCallback onPressed) {
    return Container(
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, color: color, size: 22),
        style: IconButton.styleFrom(
          padding: const EdgeInsets.all(12),
          minimumSize: const Size(48, 48),
        ),
      ),
    );
  }

  Widget _buildEnhancedAttachmentsPreview(ProblemSolverController controller) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          if (controller.imageFile != null)
            _buildEnhancedImagePreview(controller),
          if (controller.audioPath != null)
            _buildEnhancedAudioPreview(controller),
        ],
      ),
    );
  }

  Widget _buildEnhancedImagePreview(ProblemSolverController controller) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                spreadRadius: 0,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.file(
              controller.imageFile!,
              height: 80,
              width: 100,
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: -4,
          right: -4,
          child: Container(
            decoration: BoxDecoration(
              color: const Color(0xFFEF4444),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFEF4444).withOpacity(0.3),
                  spreadRadius: 0,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IconButton(
              icon: const Icon(Icons.close, color: Colors.white, size: 16),
              onPressed: controller.clearAttachments,
              style: IconButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: const Size(24, 24),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedAudioPreview(ProblemSolverController controller) {
    return Stack(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: const Color(0xFFF59E0B).withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFFF59E0B).withOpacity(0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.audiotrack, color: const Color(0xFFF59E0B), size: 20),
              const SizedBox(width: 8),
              Text(
                'Audio',
                style: TextStyle(
                  color: const Color(0xFFF59E0B),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        Positioned(
          top: -4,
          right: -4,
          child: Container(
            decoration: BoxDecoration(
              color: const Color(0xFFEF4444),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: const Icon(Icons.close, color: Colors.white, size: 16),
              onPressed: controller.clearAttachments,
              style: IconButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: const Size(24, 24),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleDoubtSolver(ProblemSolverController controller) async {
  await controller.performSearch(SearchType.doubtSolver); // Assuming performSearch is async
  _textController.clear();
 // Clear text in controller (fixes previous issue)
  
}

Future<void> _handleSearch(ProblemSolverController controller, SearchType searchType) async {
  await controller.performSearch(searchType);
  _textController.clear();
  
}

  void _showImageChoiceDialog(BuildContext context, ProblemSolverController controller) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: double.infinity,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 20),
                ListTile(
                  leading: const Icon(Icons.photo_library, color: Colors.blue),
                  title: const Text('From Gallery'),
                  onTap: () {
                    controller.pickImageFromGallery();
                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.camera_alt, color: Colors.green),
                  title: const Text('Take Photo'),
                  onTap: () {
                    controller.captureImageWithCamera();
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
