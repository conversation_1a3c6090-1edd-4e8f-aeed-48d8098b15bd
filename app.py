from flask import Flask, request, jsonify
from flask_cors import CORS
import sys
import os
import random
import string
import requests
import time
from datetime import datetime, timedelta
from pymongo import MongoClient
from bson import ObjectId
import logging
from logging.handlers import RotatingFileHandler

# Add parent directory to path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.database import Database
from shared.utils import verify_password
from shared.auth_middleware import generate_token

app = Flask(__name__)
CORS(app)

# Setup logging with rotating file handler
log_handler = RotatingFileHandler('auth_service.log', maxBytes=1000000, backupCount=5)
log_handler.setLevel(logging.DEBUG)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
log_handler.setFormatter(formatter)
app.logger.addHandler(log_handler)
app.logger.setLevel(logging.DEBUG)

# MongoDB configurations
MONGO_URI = "***********************************************/"   # Update with your MongoDB URI
mongo_client = MongoClient(MONGO_URI)
mongo_db = mongo_client["logout_database"]
session_collection = mongo_db["user_sessions"]

db = Database()

# --- SMS Gateway Hub Credentials ---
SMSGATEWAYHUB_API_KEY = "V2UAWhucjEesM8TTo5E34g"
SMSGATEWAYHUB_SENDER_ID = "DSAIS2"
ENTITY_ID = "1701174296617710373"
DLT_TEMPLATE_ID = "1707174315682876645"
REGISTERED_TEMPLATE_TEXT = "Dear Customer, {#var#} is your Dsais account verification code - DSAIS"


# Store OTPs temporarily (in production, use Redis or similar)
otp_storage = {}

def generate_otp(length=6):
    """Generate a random OTP of specified length"""
    return ''.join(random.choices(string.digits, k=length))

def send_otp(phone, otp):
    """Send OTP via SMSGATEWAYHUB API"""
    try:
        url = "https://www.smsgatewayhub.com/api/mt/SendSMS"
        template_text = REGISTERED_TEMPLATE_TEXT.replace("{#var#}", otp)

        params = {
            "APIKey": SMSGATEWAYHUB_API_KEY,
            "senderid": SMSGATEWAYHUB_SENDER_ID,
            "channel": "Trans",
            "DCS": "0",
            "flashsms": "0",
            "number": phone,
            "text": template_text,
            "entityid": ENTITY_ID,
            "dlttemplateid": DLT_TEMPLATE_ID
        }

        response = requests.get(url, params=params)
        if response.status_code == 200:
            return True
        else:
            app.logger.error(f"SMS Gateway error: {response.text}")
            return False
    except Exception as e:
        app.logger.error(f"Error sending OTP: {e}")
        return False

@app.route('/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        device = request.headers.get('User-Agent', 'unknown Device')

        if not username or not password:
            return jsonify({'message': 'Username and password required'}), 400

        # Check in users table first (Director)
        user = db.execute_query_one(
            "SELECT id, username, password, role, first_name, last_name, phone FROM users WHERE username = %s AND is_active = TRUE",
            (username,)
        )

        if user and verify_password(password, user['password']):
            # Generate OTP and store it with user info
            otp = generate_otp()
            user_id = str(user['id'])
            otp_storage[otp] = {
                'user_id': user_id,
                'role': user['role'],
                'username': user['username'],
                'first_name': user['first_name'],
                'last_name': user['last_name'],
                'device': device,
                'expires_at': time.time() + 300  # 5 minutes expiry
            }

            # Send OTP to user's phone
            if user['phone'] and send_otp(user['phone'], otp):
                app.logger.info(f"OTP sent to user {user_id} on device: {device}")
                return jsonify({'message': 'OTP sent to registered phone number'}), 200
            return jsonify({'message': 'Failed to send OTP'}), 500

        # Check in centers table (Center Counselor)
        center = db.execute_query_one(
            "SELECT id, username, password, name, phone, address, center_code FROM centers WHERE username = %s AND is_active = TRUE",
            (username,)
        )

        if center and verify_password(password, center['password']):
            # Generate OTP and store it with center info
            otp = generate_otp()
            center_id = str(center['id'])
            otp_storage[otp] = {
                'user_id': center_id,
                'role': 'center_counselor',
                'username': center['username'],
                'center_name': center['name'],
                'center_id': center_id,
                'phone': center['phone'],
                'address': center['address'],
                'center_code': center['center_code'],
                'device': device,
                'expires_at': time.time() + 300
            }

            # Send OTP to center's phone
            if center['phone'] and send_otp(center['phone'], otp):
                app.logger.info(f"OTP sent to center {center_id} on device: {device}")
                return jsonify({'message': 'OTP sent to registered phone number'}), 200
            return jsonify({'message': 'Failed to send OTP'}), 500

        # Check in students table
        student = db.execute_query_one(
            "SELECT id, username, password, first_name, last_name, center_code, phone FROM students WHERE username = %s AND is_active = TRUE",
            (username,)
        )

        if student and verify_password(password, student['password']):
            # Generate OTP
            otp = generate_otp()
            student_id = str(student['id'])
            otp_storage[otp] = {
                'user_id': student_id,
                'role': 'student',
                'username': student['username'],
                'first_name': student['first_name'],
                'last_name': student['last_name'],
                'center_code': student['center_code'],
                'device': device,
                'expires_at': time.time() + 300
            }

            # Send OTP to student's phone
            if student['phone'] and send_otp(student['phone'], otp):
                app.logger.info(f"OTP sent to student {student_id} on device: {device}")
                return jsonify({'message': 'OTP sent to registered phone number'}), 200
            return jsonify({'message': 'Failed to send OTP'}), 500

        # Check in parents table
        parent = db.execute_query_one(
            "SELECT id, username, password, first_name, last_name, student_id, phone FROM parents WHERE username = %s AND is_active = TRUE",
            (username,)
        )

        if parent and verify_password(password, parent['password']):
            # Generate OTP
            otp = generate_otp()
            parent_id = str(parent['id'])
            otp_storage[otp] = {
                'user_id': parent_id,
                'role': 'parent',
                'username': parent['username'],
                'first_name': parent['first_name'],
                'last_name': parent['last_name'],
                'student_id': str(parent['student_id']),
                'device': device,
                'expires_at': time.time() + 300
            }

            # Send OTP to parent's phone
            if parent['phone'] and send_otp(parent['phone'], otp):
                app.logger.info(f"OTP sent to parent {parent_id} on device: {device}")
                return jsonify({'message': 'OTP sent to registered phone number'}), 200
            return jsonify({'message': 'Failed to send OTP'}), 500

        # Check in faculty table
        faculty = db.execute_query_one(
            "SELECT id, username, password, first_name, last_name, center_code, phone FROM faculty WHERE username = %s AND is_active = TRUE",
            (username,)
        )

        if faculty and verify_password(password, faculty['password']):
            # Generate OTP
            otp = generate_otp()
            faculty_id = str(faculty['id'])
            otp_storage[otp] = {
                'user_id': faculty_id,
                'role': 'faculty',
                'username': faculty['username'],
                'first_name': faculty['first_name'],
                'last_name': faculty['last_name'],
                'center_code': faculty['center_code'],
                'device': device,
                'expires_at': time.time() + 300
            }

            # Send OTP to faculty's phone
            if faculty['phone'] and send_otp(faculty['phone'], otp):
                app.logger.info(f"OTP sent to faculty {faculty_id} on device: {device}")
                return jsonify({'message': 'OTP sent to registered phone number'}), 200
            return jsonify({'message': 'Failed to send OTP'}), 500

        # Check in kota_teachers table
        teacher = db.execute_query_one(
            "SELECT id, username, password, first_name, last_name, phone, designation FROM kota_teachers WHERE username = %s AND is_active = TRUE",
            (username,)
        )

        if teacher and verify_password(password, teacher['password']):
            # Generate OTP
            otp = generate_otp()
            teacher_id = str(teacher['id'])
            otp_storage[otp] = {
                'user_id': teacher_id,
                'role': 'kota_teacher',
                'username': teacher['username'],
                'first_name': teacher['first_name'],
                'last_name': teacher['last_name'],
                'designation': teacher['designation'],
                'device': device,
                'expires_at': time.time() + 300
            }

            # Send OTP to teacher's phone
            if teacher['phone'] and send_otp(teacher['phone'], otp):
                app.logger.info(f"OTP sent to teacher {teacher_id} on device: {device}")
                return jsonify({'message': 'OTP sent to registered phone number'}), 200
            return jsonify({'message': 'Failed to send OTP'}), 500

        # Check in mendor table
        mendor = db.execute_query_one(
            "SELECT id, username, password, first_name, last_name, phone, course_id, course_name, subject_id, subject_name FROM mendor WHERE username = %s AND is_active = TRUE",
            (username,)
        )

        if mendor and verify_password(password, mendor['password']):
            # Generate OTP
            otp = generate_otp()
            mendor_id = str(mendor['id'])
            otp_storage[otp] = {
                'user_id': mendor_id,
                'role': 'mendor',
                'username': mendor['username'],
                'first_name': mendor['first_name'],
                'last_name': mendor['last_name'],
                'course_id': str(mendor['course_id']),
                'course_name': mendor['course_name'],
                'subject_id': str(mendor['subject_id']),
                'subject_name': mendor['subject_name'],
                'device': device,
                'expires_at': time.time() + 300
            }

            # Send OTP to mendor's phone
            if mendor['phone'] and send_otp(mendor['phone'], otp):
                app.logger.info(f"OTP sent to mendor {mendor_id} on device: {device}")
                return jsonify({'message': 'OTP sent to registered phone number'}), 200
            return jsonify({'message': 'Failed to send OTP'}), 500

        app.logger.warning(f"Invalid login attempt for username: {username}")
        return jsonify({'message': 'Invalid credentials'}), 401

    except Exception as e:
        app.logger.error(f"Login error: {e}")
        return jsonify({'message': 'Internal server error'}), 500

@app.route('/verify-otp', methods=['POST'])
def verify_otp():
    try:
        data = request.get_json()
        entered_otp = data.get('otp')

        if not entered_otp:
            return jsonify({'message': 'OTP required'}), 400

        # Check if OTP exists and is valid
        if entered_otp not in otp_storage:
            app.logger.warning(f"Invalid or expired OTP: {entered_otp}")
            return jsonify({'message': 'OTP not found or expired'}), 400

        stored_data = otp_storage[entered_otp]
        if time.time() > stored_data['expires_at']:
            del otp_storage[entered_otp]
            app.logger.warning(f"Expired OTP: {entered_otp}")
            return jsonify({'message': 'OTP expired'}), 400

        # OTP is valid, generate token based on role
        role = stored_data['role']
        user_id = stored_data['user_id']
        device = stored_data['device']

        # Invalidate previous sessions for this user
        session_collection.update_many(
            {"user_id": user_id, "active": True},
            {"$set": {"active": False, "signout_time": time.time()}}
        )

        # Create new session
        session_data = {
            "user_id": user_id,
            "role": role,
            "device": device,
            "login_time": time.time(),
            "active": True
        }
        result = session_collection.insert_one(session_data)
        active_session_id = str(result.inserted_id)

        if role == 'director':
            user = db.execute_query_one(
                "SELECT id, username, role, first_name, last_name FROM users WHERE id = %s AND is_active = TRUE",
                (user_id,)
            )
            if user:
                token = generate_token(str(user['id']), user['username'], user['role'])
                del otp_storage[entered_otp]
                app.logger.info(f"User {user_id} verified OTP and logged in successfully")
                return jsonify({
                    'token': token,
                    'active_session_id': active_session_id,
                    'user': {
                        'id': str(user['id']),
                        'username': user['username'],
                        'role': user['role'],
                        'first_name': user['first_name'],
                        'last_name': user['last_name']
                    }
                }), 200

        elif role == 'center_counselor':
            center = db.execute_query_one(
                "SELECT id, username, name, phone, address, center_code FROM centers WHERE id = %s AND is_active = TRUE",
                (user_id,)
            )
            if center:
                token = generate_token(str(center['id']), center['username'], 'center_counselor')
                del otp_storage[entered_otp]
                app.logger.info(f"Center {user_id} verified OTP and logged in successfully")
                return jsonify({
                    'token': token,
                    'active_session_id': active_session_id,
                    'user': {
                        'id': str(center['id']),
                        'username': center['username'],
                        'role': 'center_counselor',
                        'center_name': center['name'],
                        'center_id': str(center['id']),
                        'phone': center['phone'],
                        'address': center['address'],
                        'center_code': center['center_code']
                    }
                }), 200

        elif role == 'student':
            student = db.execute_query_one(
                "SELECT id, username, first_name, last_name, center_code FROM students WHERE id = %s AND is_active = TRUE",
                (user_id,)
            )
            if student:
                token = generate_token(str(student['id']), student['username'], 'student')
                del otp_storage[entered_otp]
                app.logger.info(f"Student {user_id} verified OTP and logged in successfully")
                return jsonify({
                    'token': token,
                    'active_session_id': active_session_id,
                    'user': {
                        'id': str(student['id']),
                        'username': student['username'],
                        'role': 'student',
                        'first_name': student['first_name'],
                        'last_name': student['last_name'],
                        'center_code': student['center_code']
                    }
                }), 200

        elif role == 'parent':
            parent = db.execute_query_one(
                "SELECT id, username, first_name, last_name, student_id FROM parents WHERE id = %s AND is_active = TRUE",
                (user_id,)
            )
            if parent:
                token = generate_token(str(parent['id']), parent['username'], 'parent')
                del otp_storage[entered_otp]
                app.logger.info(f"Parent {user_id} verified OTP and logged in successfully")
                return jsonify({
                    'token': token,
                    'active_session_id': active_session_id,
                    'user': {
                        'id': str(parent['id']),
                        'username': parent['username'],
                        'role': 'parent',
                        'first_name': parent['first_name'],
                        'last_name': parent['last_name'],
                        'student_id': str(parent['student_id'])
                    }
                }), 200

        elif role == 'faculty':
            faculty = db.execute_query_one(
                "SELECT id, username, first_name, last_name, center_code FROM faculty WHERE id = %s AND is_active = TRUE",
                (user_id,)
            )
            if faculty:
                token = generate_token(str(faculty['id']), faculty['username'], 'faculty')
                del otp_storage[entered_otp]
                app.logger.info(f"Faculty {user_id} verified OTP and logged in successfully")
                return jsonify({
                    'token': token,
                    'active_session_id': active_session_id,
                    'user': {
                        'id': str(faculty['id']),
                        'username': faculty['username'],
                        'role': 'faculty',
                        'first_name': faculty['first_name'],
                        'last_name': faculty['last_name'],
                        'center_code': faculty['center_code']
                    }
                }), 200

        elif role == 'kota_teacher':
            teacher = db.execute_query_one(
                "SELECT id, username, first_name, last_name, designation FROM kota_teachers WHERE id = %s AND is_active = TRUE",
                (user_id,)
            )
            if teacher:
                token = generate_token(str(teacher['id']), teacher['username'], 'kota_teacher')
                del otp_storage[entered_otp]
                app.logger.info(f"Teacher {user_id} verified OTP and logged in successfully")
                return jsonify({
                    'token': token,
                    'active_session_id': active_session_id,
                    'user': {
                        'id': str(teacher['id']),
                        'username': teacher['username'],
                        'role': 'kota_teacher',
                        'first_name': teacher['first_name'],
                        'last_name': teacher['last_name'],
                        'designation': teacher['designation']
                    }
                }), 200

        elif role == 'mendor':
            mendor = db.execute_query_one(
                "SELECT id, username, first_name, last_name, course_id, course_name, subject_id, subject_name FROM mendor WHERE id = %s AND is_active = TRUE",
                (user_id,)
            )
            if mendor:
                token = generate_token(str(mendor['id']), mendor['username'], 'mendor')
                del otp_storage[entered_otp]
                app.logger.info(f"Mendor {user_id} verified OTP and logged in successfully")
                return jsonify({
                    'token': token,
                    'active_session_id': active_session_id,
                    'user': {
                        'id': str(mendor['id']),
                        'username': mendor['username'],
                        'role': 'mendor',
                        'first_name': mendor['first_name'],
                        'last_name': mendor['last_name'],
                        'course_id': str(mendor['course_id']),
                        'course_name': mendor['course_name'],
                        'subject_id': str(mendor['subject_id']),
                        'subject_name': mendor['subject_name']
                    }
                }), 200

        app.logger.warning(f"Invalid user or role for OTP: {entered_otp}")
        return jsonify({'message': 'Invalid user or role'}), 400

    except Exception as e:
        app.logger.error(f"OTP verification error: {e}")
        return jsonify({'message': 'Internal server error'}), 500

@app.route('/verify-token', methods=['POST'])
def verify_token():
    try:
        from shared.auth_middleware import token_required

        @token_required()
        def verify():
            return jsonify({
                'valid': True,
                'user': request.current_user
            }), 200

        return verify()

    except Exception as e:
        app.logger.error(f"Token verification error: {e}")
        return jsonify({'valid': False, 'message': str(e)}), 401

@app.route('/validate-session', methods=['POST'])
def validate_session():
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        active_session_id = data.get('active_session_id')

        if not user_id or not active_session_id:
            app.logger.warning("Missing user_id or active_session_id in validate-session request")
            return jsonify({'message': 'Missing user ID or session ID'}), 400

        try:
            session_object_id = ObjectId(active_session_id)
        except ValueError:
            app.logger.warning(f"Invalid session ID format: {active_session_id}")
            return jsonify({'message': 'Invalid session ID format'}), 400

        active_session = session_collection.find_one({
            'user_id': user_id,
            '_id': session_object_id,
            'active': True
        })

        if active_session:
            if active_session['device'] != request.headers.get('User-Agent', 'Unknown Device'):
                session_collection.update_one(
                    {'_id': active_session['_id']},
                    {'$set': {'active': False, 'signout_time': time.time()}}
                )
                app.logger.warning(f"Session terminated for user {user_id} due to device mismatch")
                return jsonify({'message': 'Session terminated due to login from another device'}), 403

            app.logger.info(f"Session validated for user {user_id}")
            return jsonify({'message': 'Session is valid'}), 200

        app.logger.warning(f"Invalid session for user {user_id}, active_session_id: {active_session_id}")
        return jsonify({'message': 'Session is invalid. Please log in again'}), 403

    except Exception as e:
        app.logger.error(f"Session validation error: {e}")
        return jsonify({'message': 'Session validation failed', 'error': str(e)}), 500

@app.route('/logout', methods=['POST'])
def logout():
    try:
        data = request.get_json()
        user_id = data.get('user_id')

        if not user_id:
            app.logger.warning("Missing user_id in logout request")
            return jsonify({'message': 'Missing user_id'}), 400

        # Find user based on user_id across all possible tables
        user = db.execute_query_one(
            "SELECT id AS user_id, 'director' AS role, username FROM users WHERE id = %s AND is_active = TRUE",
            (user_id,)
        )
        if not user:
            user = db.execute_query_one(
                "SELECT id AS user_id, 'center_counselor' AS role, username FROM centers WHERE id = %s AND is_active = TRUE",
                (user_id,)
            )
        if not user:
            user = db.execute_query_one(
                "SELECT id AS user_id, 'student' AS role, username FROM students WHERE id = %s AND is_active = TRUE",
                (user_id,)
            )
        if not user:
            user = db.execute_query_one(
                "SELECT id AS user_id, 'parent' AS role, username FROM parents WHERE id = %s AND is_active = TRUE",
                (user_id,)
            )
        if not user:
            user = db.execute_query_one(
                "SELECT id AS user_id, 'faculty' AS role, username FROM faculty WHERE id = %s AND is_active = TRUE",
                (user_id,)
            )
        if not user:
            user = db.execute_query_one(
                "SELECT id AS user_id, 'kota_teacher' AS role, username FROM kota_teachers WHERE id = %s AND is_active = TRUE",
                (user_id,)
            )
        if not user:
            user = db.execute_query_one(
                "SELECT id AS user_id, 'mendor' AS role, username FROM mendor WHERE id = %s AND is_active = TRUE",
                (user_id,)
            )

        if not user:
            app.logger.warning(f"No active user found for user_id: {user_id}")
            return jsonify({'message': 'User not found'}), 404

        # Invalidate all active sessions for this user
        session_collection.update_many(
            {'user_id': str(user_id), 'active': True},
            {'$set': {'active': False, 'signout_time': time.time()}}
        )

        app.logger.info(f"User ID: {user_id}, Username: {user['username']}, Role: {user['role']} logged out successfully")
        return jsonify({'message': 'Logout successful'}), 200

    except Exception as e:
        app.logger.error(f"Logout error: {e}")
        return jsonify({'message': 'Logout failed', 'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'Auth Service (UUID)',
        'message': 'Authentication service is running with UUID, OTP, and session management support'
    }), 200

if __name__ == '__main__':
    app.run(host="0.0.0.0", debug=True, port=8001)
