import 'package:flutter/material.dart';
import '../../../../../core/config/app_config.dart';
import '../../model/dashboard_model.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../model/dashboard_model.dart';

class ProfileCard extends StatelessWidget {
  final Student student; //

  const ProfileCard({super.key, required this.student});

  @override
  Widget build(BuildContext context) {
    return Animate(
      effects: [
        FadeEffect(duration: 800.ms),
        SlideEffect(begin: const Offset(0, 0.5), end: Offset.zero),
      ],
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        ),
        elevation: 4,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  Animate(
                    effects: [
                      MoveEffect(
                        begin: const Offset(0, 0),
                        end: const Offset(0, -15),
                        duration: 2000.ms,
                        curve: Curves.easeInOut,
                      ),
                    ],
                    child: CircleAvatar(
                      radius: 40,
                      backgroundColor:
                          Color(AppConfig.roleColors['student']!),
                      child: Text(
                        '${student.firstName?[0] ?? ''}${student.lastName?[0] ?? ''}'
                            .toUpperCase(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 32,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${student.firstName ?? student.username} ${student.lastName ?? ''}',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Color(AppConfig.roleColors['student']!),
                        ),
                      ),
                      Chip(
                        label: Text(student.course),
                        avatar: const Icon(Icons.book, size: 16),
                        backgroundColor:
                            Color(AppConfig.roleColors['counselor']!),
                        labelStyle: const TextStyle(color: Colors.white),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: Card(
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8)),
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text('Personal Info',
                                style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold)),
                            const SizedBox(height: 8),
                            ListTile(
                              leading:
                                  const Icon(Icons.email, color: Colors.blue),
                              title: Text(student.studentEmail),
                              onTap: () => launchUrl(Uri.parse(
                                  'mailto:${student.studentEmail}')),
                            ),
                            ListTile(
                              leading:
                                  const Icon(Icons.phone, color: Colors.blue),
                              title: Text(student.phone),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Card(
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8)),
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text('Education',
                                style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold)),
                            const SizedBox(height: 8),
                            ListTile(
                              leading:
                                  const Icon(Icons.school, color: Colors.blue),
                              title: Text(student.centerName),
                            ),
                            ListTile(
                              leading:
                                  const Icon(Icons.tag, color: Colors.blue),
                              title:
                                  Text('Center Code: ${student.centerCode}'),
                            ),
                            ListTile(
                              leading:
                                  const Icon(Icons.email, color: Colors.blue),
                              title: Text(student.centerEmail),
                              onTap: () => launchUrl(Uri.parse(
                                  'mailto:${student.centerEmail}')),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
