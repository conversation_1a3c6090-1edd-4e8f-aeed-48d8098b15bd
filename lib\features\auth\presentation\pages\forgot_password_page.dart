/// forgot_password_page.dart - Match Login UI (No Gradients, Solid Brand Colors)
///
/// - Mirrors the first login UI: solid brand header with rounded bottom, circular white logo plate,
///   modern card form, clear focus states, and solid buttons (no gradients).
/// - Uses the same brand palette as login: Blue (#1E88E5) primary, Green (#4CAF50) accent.
/// - Preserves existing logic: send OTP, then reset password with OTP + new password.
/// - Subtle animations for header/logo and form opacity, similar to the login page.
/// - Uses AnimatedInputField for consistent inputs, and status bar styling like the login page.
library;

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:http/http.dart' as http;
import '../../../../core/config/app_config.dart';
import '../../../../core/theme/app_theme.dart';
import '../widgets/animated_input_field.dart';

class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> with TickerProviderStateMixin {
  // Brand palette (match login.dart)
  static const kBrandPrimary = Color(0xFF1E88E5); // Blue
  static const kBrandAccent = Color(0xFF4CAF50);  // Green
  static const kBackground = Color(0xFFF6F8FB);  // Neutral soft bg
  static const kSurface = Colors.white;

  final _formKey = GlobalKey<FormState>();

  final _emailController = TextEditingController();
  final _otpController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  final _emailFocus = FocusNode();
  final _otpFocus = FocusNode();
  final _newPasswordFocus = FocusNode();
  final _confirmPasswordFocus = FocusNode();

  bool _isLoading = false;
  bool _isReset = false; // false => Send OTP view; true => Reset Password view
  bool _showPassword = false;

  late AnimationController _fadeController;
  late AnimationController _logoController;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();

    // Match login: status bar on brand color with light icons
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: kBrandPrimary,
        statusBarIconBrightness: Brightness.light,
      ),
    );
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _logoController.forward();
    Future.delayed(const Duration(milliseconds: 150), () {
      if (mounted) _fadeController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _logoController.dispose();
    _emailController.dispose();
    _otpController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    _emailFocus.dispose();
    _otpFocus.dispose();
    _newPasswordFocus.dispose();
    _confirmPasswordFocus.dispose();
    super.dispose();
  }

  Future<void> _handleForgotPassword() async {
    if (_formKey.currentState?.validate() != true) return;

    setState(() => _isLoading = true);
    try {
      final response = await http.post(
        Uri.parse('${AppConfig.baseUrl}${AppConfig.forgetPasswordEndpoint}'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'email': _emailController.text}),
      );

      final responseBody = jsonDecode(response.body);
      debugPrint('Forgot Password - Status: ${response.statusCode}');
      debugPrint('Forgot Password - Body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(responseBody['message'] ?? 'Password reset OTP sent!')),
          );
        }
        setState(() => _isReset = true);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(responseBody['error'] ?? 'Failed to send OTP')),
          );
        }
      }
    } catch (e) {
      debugPrint('Forgot Password - Error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Network error. Please try again.')),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  Future<void> _handleResetPassword() async {
    if (_newPasswordController.text != _confirmPasswordController.text) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Passwords do not match')),
      );
      return;
    }
    if (_formKey.currentState?.validate() != true) return;

    setState(() => _isLoading = true);
    try {
      final response = await http.post(
        Uri.parse('${AppConfig.baseUrl}${AppConfig.resetPasswordEndpoint}'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': _emailController.text,
          'otp': _otpController.text,
          'new_password': _newPasswordController.text,
        }),
      );

      final responseBody = jsonDecode(response.body);
      debugPrint('Reset Password - Status: ${response.statusCode}');
      debugPrint('Reset Password - Body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(responseBody['message'] ?? 'Password reset successful!')),
          );
          context.go('/auth/login');
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(responseBody['error'] ?? 'Failed to reset password')),
          );
        }
      }
    } catch (e) {
      debugPrint('Reset Password - Error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Network error. Please try again.')),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kBackground,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.only(bottom: 24),
          child: Column(
            children: [
              _buildHeader(), // same visual structure as login
              const SizedBox(height: 16),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: _buildCardForm(), // card with inputs like login
              ),
              const SizedBox(height: 16),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  // Header matches login.dart: brand primary bg, rounded bottom, centered circular logo
  Widget _buildHeader() {
    return AnimatedBuilder(
      animation: _logoController,
      builder: (context, child) {
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.fromLTRB(20, 28, 20, 28),
          decoration: const BoxDecoration(
            color: kBrandPrimary,
            borderRadius: BorderRadius.vertical(bottom: Radius.circular(24)),
          ),
          child: Opacity(
            opacity: _logoController.value,
            child: Transform.scale(
              scale: 0.95 + (_logoController.value * 0.05),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: 96,
                    height: 96,
                    decoration: const BoxDecoration(
                      color: kSurface,
                      shape: BoxShape.circle,
                    ),
                    alignment: Alignment.center,
                    child: Image.asset(
                      'assets/icons/sasthra_logo.png',
                      width: 120,
                      height: 120,
                      fit: BoxFit.contain,
                      errorBuilder: (_, __, ___) => const Icon(
                        Icons.school,
                        size: 40,
                        color: kBrandPrimary,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _isReset ? 'Reset Your Password' : 'Forgot Password',
                    textAlign: TextAlign.center,
                    style: AppTheme.headingLarge.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    _isReset
                        ? 'Enter OTP and your new password'
                        : 'Enter your email to receive a reset OTP',
                    textAlign: TextAlign.center,
                    style: AppTheme.bodyMedium.copyWith(
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCardForm() {
    return AnimatedBuilder(
      animation: _fadeController,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeController.value,
          child: Card(
            elevation: 2,
            color: kSurface,
            shadowColor: Colors.black.withOpacity(0.06),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(color: Colors.black.withOpacity(0.06)),
            ),
            child: Padding(
              padding: const EdgeInsets.fromLTRB(20, 20, 20, 16),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    // Email field (always visible)
                    _buildSolidInput(
                      focusNode: _emailFocus,
                      child: AnimatedInputField(
                        controller: _emailController,
                        focusNode: _emailFocus,
                        label: 'Email',
                        hint: 'Enter your email',
                        prefixIconData: Icons.email_outlined,
                        textInputAction: TextInputAction.next,
                        onFieldSubmitted: (_) =>
                            _isReset ? _otpFocus.requestFocus() : _handleForgotPassword(),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your email';
                          }
                          final emailRegex =
                              RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                          if (!emailRegex.hasMatch(value)) {
                            return 'Please enter a valid email';
                          }
                          return null;
                        },
                        cursorColor: kBrandPrimary,
                        decoration: _inputDecoration(
                          label: 'Email',
                          hint: 'Enter your email',
                          icon: Icons.email_outlined,
                          isFocused: _emailFocus.hasFocus,
                        ),
                      ),
                    ),
                    if (_isReset) ...[
                      const SizedBox(height: 14),
                      // OTP
                      _buildSolidInput(
                        focusNode: _otpFocus,
                        child: AnimatedInputField(
                          controller: _otpController,
                          focusNode: _otpFocus,
                          label: 'OTP',
                          hint: 'Enter the OTP',
                          prefixIconData: Icons.lock_outline,
                          textInputAction: TextInputAction.next,
                          keyboardType: TextInputType.number,
                          onFieldSubmitted: (_) => _newPasswordFocus.requestFocus(),
                          validator: (value) =>
                              (value == null || value.isEmpty) ? 'Please enter the OTP' : null,
                          cursorColor: kBrandPrimary,
                          decoration: _inputDecoration(
                            label: 'OTP',
                            hint: 'Enter the OTP',
                            icon: Icons.lock_outline,
                            isFocused: _otpFocus.hasFocus,
                          ),
                        ),
                      ),
                      const SizedBox(height: 14),
                      // New Password
                      _buildSolidInput(
                        focusNode: _newPasswordFocus,
                        child: AnimatedInputField(
                          controller: _newPasswordController,
                          focusNode: _newPasswordFocus,
                          label: 'New Password',
                          hint: 'Enter new password',
                          prefixIconData: Icons.lock_outline,
                          obscureText: !_showPassword,
                          textInputAction: TextInputAction.next,
                          onFieldSubmitted: (_) => _confirmPasswordFocus.requestFocus(),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a new password';
                            }
                            if (value.length < 6) {
                              return 'Password must be at least 6 characters';
                            }
                            return null;
                          },
                          cursorColor: kBrandPrimary,
                          suffixIcon: IconButton(
                            icon: Icon(
                              _showPassword ? Icons.visibility : Icons.visibility_off,
                              color: _showPassword ? kBrandPrimary : AppTheme.textSecondary,
                            ),
                            onPressed: () => setState(() => _showPassword = !_showPassword),
                            tooltip: _showPassword ? 'Hide password' : 'Show password',
                          ),
                          decoration: _inputDecoration(
                            label: 'New Password',
                            hint: 'Enter new password',
                            icon: Icons.lock_outline,
                            isFocused: _newPasswordFocus.hasFocus,
                          ),
                        ),
                      ),
                      const SizedBox(height: 14),
                      // Confirm Password
                      _buildSolidInput(
                        focusNode: _confirmPasswordFocus,
                        child: AnimatedInputField(
                          controller: _confirmPasswordController,
                          focusNode: _confirmPasswordFocus,
                          label: 'Confirm Password',
                          hint: 'Confirm new password',
                          prefixIconData: Icons.lock_outline,
                          obscureText: !_showPassword,
                          textInputAction: TextInputAction.done,
                          onFieldSubmitted: (_) => _handleResetPassword(),
                          validator: (value) =>
                              (value == null || value.isEmpty) ? 'Please confirm your password' : null,
                          cursorColor: kBrandPrimary,
                          suffixIcon: IconButton(
                            icon: Icon(
                              _showPassword ? Icons.visibility : Icons.visibility_off,
                              color: _showPassword ? kBrandPrimary : AppTheme.textSecondary,
                            ),
                            onPressed: () => setState(() => _showPassword = !_showPassword),
                            tooltip: _showPassword ? 'Hide password' : 'Show password',
                          ),
                          decoration: _inputDecoration(
                            label: 'Confirm Password',
                            hint: 'Confirm new password',
                            icon: Icons.lock_outline,
                            isFocused: _confirmPasswordFocus.hasFocus,
                          ),
                        ),
                      ),
                    ],
                    const SizedBox(height: 20),

                    // Primary action (solid brand button)
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading
                            ? null
                            : (_isReset ? _handleResetPassword : _handleForgotPassword),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: kBrandPrimary,
                          disabledBackgroundColor: kBrandPrimary.withOpacity(0.6),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                        ),
                        child: _isLoading
                            ? Row(
                                mainAxisSize: MainAxisSize.min,
                                children: const [
                                  SizedBox(
                                    height: 18,
                                    width: 18,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                    ),
                                  ),
                                  SizedBox(width: 10),
                                  Text('Please wait...'),
                                ],
                              )
                            : Text(_isReset ? 'Reset Password' : 'Send OTP',
                                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
                      ),
                    ),

                    // Secondary link to switch back to first step (visible in reset view)
                    if (_isReset) ...[
                      const SizedBox(height: 8),
                      Align(
                        alignment: Alignment.centerRight,
                        child: TextButton(
                          onPressed: () {
                            setState(() {
                              _isReset = false;
                              _otpController.clear();
                              _newPasswordController.clear();
                              _confirmPasswordController.clear();
                            });
                          },
                          style: TextButton.styleFrom(foregroundColor: kBrandPrimary),
                          child: Text(
                            'Back to Forgot Password',
                            style: AppTheme.bodyMedium.copyWith(
                              
                              decorationColor: kBrandPrimary,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // Same wrapper used in login to provide subtle focus surface
  Widget _buildSolidInput({required FocusNode focusNode, required Widget child}) {
    final isFocused = focusNode.hasFocus;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 180),
      padding: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        color: isFocused ? kBrandAccent.withOpacity(0.06) : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
      ),
      child: child,
    );
  }

  // Match login.dart input decoration
  InputDecoration _inputDecoration({
    required String label,
    required String hint,
    required IconData icon,
    required bool isFocused,
  }) {
    return InputDecoration(
      labelText: label,
      hintText: hint,
      filled: true,
      fillColor: const Color(0xFFF9FAFB),
      contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 14),
      prefixIcon: Icon(icon, color: isFocused ? kBrandPrimary : AppTheme.textSecondary),
      labelStyle: TextStyle(
        color: isFocused ? kBrandPrimary : AppTheme.textSecondary,
        fontWeight: FontWeight.w500,
      ),
      hintStyle: TextStyle(color: AppTheme.textSecondary.withOpacity(0.6)),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: Colors.black.withOpacity(0.08), width: 1),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: kBrandPrimary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: Colors.red, width: 2),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: Colors.red, width: 2),
      ),
    );
  }

  Widget _buildFooter() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          TextButton(
            onPressed: () => context.go('/auth/login'),
            child: Text(
              'Back to Login',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
                
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '© 2025 Sasthra. All rights reserved.',
            textAlign: TextAlign.center,
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.textTertiary,
            ),
          ),
        ],
      ),
    );
  }
}