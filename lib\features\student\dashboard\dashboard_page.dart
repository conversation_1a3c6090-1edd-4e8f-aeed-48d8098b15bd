import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../../core/widgets/base_page.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/logger.dart';
import '../onboarding_assessment/view/onboardingass_page.dart';
//import '../onboarding_assessment/service/onboardingass.service.dart';
import '../onboarding_assessment/controller/onboardingass_controller.dart';

class StudentDashboardPage extends ConsumerStatefulWidget {
  const StudentDashboardPage({Key? key}) : super(key: key);

  @override
  ConsumerState<StudentDashboardPage> createState() => _StudentDashboardPageState();
}

class _StudentDashboardPageState extends ConsumerState<StudentDashboardPage> {
  bool _isLoading = false;
  Map<String, dynamic> _dashboardData = {};
  String _activeTab = 'overview'; // Track active tab

  // Tab configuration
  final List<Map<String, String>> _tabItems = [
    {'value': 'overview', 'label': 'Overview', 'shortLabel': 'Overview'},
    {'value': 'onboarding-assessment', 'label': 'Onboarding Assessment', 'shortLabel': 'Onboarding'},
    {'value': 'performance', 'label': 'Performance Based', 'shortLabel': 'Performance'},
    {'value': 'test-performance', 'label': 'Test Performance', 'shortLabel': 'Test Perf'},
    {'value': 'test-breakdown', 'label': 'Test Breakdown', 'shortLabel': 'Breakdown'},
    {'value': 'time-analysis', 'label': 'Time Analysis', 'shortLabel': 'Time'},
    {'value': 'cbt', 'label': 'CBT', 'shortLabel': 'Cbt'},
  ];

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    setState(() => _isLoading = true);
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));
    
    setState(() {
      _dashboardData = {
        'performance': {
          'overallScore': 78.5,
          'rank': 1234,
          'testsCompleted': 45,
          'studyHours': 156,
        },
        'subjects': [
          {'name': 'Physics', 'score': 82, 'progress': 0.82, 'color': Colors.blue},
          {'name': 'Chemistry', 'score': 75, 'progress': 0.75, 'color': Colors.green},
          {'name': 'Mathematics', 'score': 88, 'progress': 0.88, 'color': Colors.orange},
          {'name': 'Biology', 'score': 70, 'progress': 0.70, 'color': Colors.purple},
        ],
        'recentActivity': [
          {'type': 'test', 'title': 'Physics Mock Test #14', 'score': 85, 'date': DateTime.now().subtract(const Duration(hours: 2))},
          {'type': 'study', 'title': 'Chemistry Video Lecture', 'duration': 45, 'date': DateTime.now().subtract(const Duration(hours: 5))},
          {'type': 'practice', 'title': 'Math Problem Set #12', 'questions': 25, 'date': DateTime.now().subtract(const Duration(days: 1))},
        ],
        'upcomingEvents': [
          {'type': 'test', 'title': 'JEE Main Mock Test #15', 'date': DateTime.now().add(const Duration(hours: 18))},
          {'type': 'live', 'title': 'Physics Live Class', 'date': DateTime.now().add(const Duration(days: 1, hours: 2))},
        ],
        'goals': [
          {'title': 'Complete 50 Mock Tests', 'current': 45, 'target': 50, 'progress': 0.9},
          {'title': 'Study 200 Hours', 'current': 156, 'target': 200, 'progress': 0.78},
          {'title': 'Achieve Rank < 1000', 'current': 1234, 'target': 1000, 'progress': 0.65},
        ],
      };
      _isLoading = false;
    });
    
    AppLogger.userAction('Student dashboard loaded');
  }

  void _handleTabChange(String value) {
    setState(() {
      _activeTab = value;
    });
    AppLogger.userAction('Switched to tab: $value');
  }

  @override
  Widget build(BuildContext context) {
    return BasePage(
      title: 'Dashboard',
      subtitle: 'Your learning analytics and insights',
      breadcrumbs: const ['Dashboard', 'Student', 'Dashboard'],
      isLoading: _isLoading,
      actions: [
        IconButton(
          onPressed: _loadDashboardData,
          icon: const Icon(Icons.refresh),
          tooltip: 'Refresh',
        ),
      ],
      child: Column(
        children: [
          // Tab Navigation
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: _tabItems.map((tab) {
                final isActive = _activeTab == tab['value'];
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: GestureDetector(
                    onTap: () => _handleTabChange(tab['value']!),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: isActive ? Colors.white : Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: isActive
                            ? [BoxShadow(color: Colors.grey.withOpacity(0.2), blurRadius: 4)]
                            : null,
                      ),
                      child: Text(
                        MediaQuery.of(context).size.width < 600
                            ? tab['shortLabel']!
                            : tab['label']!,
                        style: TextStyle(
                          color: isActive ? AppTheme.textPrimary : AppTheme.textSecondary,
                          fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
          // Tab Content
          Expanded(
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (child, animation) => FadeTransition(
                opacity: animation,
                child: child,
              ),
              child: _buildTabContent(),
            ),
          ),
        ],
      ),
    );
  }
  Future<void> _checkStartAssessment() async {
    try {
      final data = await ref.read(onboardDataProvider.future);
      print('Onboard assessment data on skip: $data');
    } catch (e) {
      print('Error fetching onboard assessment: $e');
    }
  }


  Widget _buildTabContent() {
  switch (_activeTab) {
    case 'overview':
      return _buildDashboardContent();
    case 'onboarding-assessment':
      return OnboardingAssessmentPage(
        onSkip: () {
          print('User skipped the onboarding assessment');
          setState(() {
            _activeTab = 'overview'; // Switch to dashboard tab on skip
          });
          _checkStartAssessment();
        },
      );
    // Placeholder for other tabs
    case 'performance':
    case 'test-performance':
    case 'test-breakdown':
    case 'time-analysis':
    case 'cbt':
      return Center(
        child: Text(
          '$_activeTab Dashboard\nUnder Development',
          style: AppTheme.headingMedium,
          textAlign: TextAlign.center,
        ),
      );
    default:
      return const SizedBox.shrink();
  }
}

  Widget _buildDashboardContent() {
    if (_dashboardData.isEmpty) return const SizedBox.shrink();
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPerformanceOverview(),
          const SizedBox(height: 24),
          _buildSubjectProgress(),
          const SizedBox(height: 24),
          _buildGoalsSection(),
          const SizedBox(height: 24),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(child: _buildRecentActivity()),
              const SizedBox(width: 16),
              Expanded(child: _buildUpcomingEvents()),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceOverview() {
    final performance = _dashboardData['performance'] as Map<String, dynamic>;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF667eea), Color(0xFF764ba2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Performance Overview',
            style: AppTheme.headingMedium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(child: _buildPerformanceCard('Overall Score', '${performance['overallScore']}%', Icons.trending_up)),
              const SizedBox(width: 12),
              Expanded(child: _buildPerformanceCard('Current Rank', '#${performance['rank']}', Icons.emoji_events)),
              const SizedBox(width: 12),
              Expanded(child: _buildPerformanceCard('Tests Done', '${performance['testsCompleted']}', Icons.quiz)),
              const SizedBox(width: 12),
              Expanded(child: _buildPerformanceCard('Study Hours', '${performance['studyHours']}h', Icons.schedule)),
            ],
          ),
        ],
      ),
    ).animate().fadeIn().scale(begin: const Offset(0.9, 0.9));
  }

  Widget _buildPerformanceCard(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.white, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: AppTheme.bodyLarge.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppTheme.bodySmall.copyWith(
              color: Colors.white.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSubjectProgress() {
    final subjects = _dashboardData['subjects'] as List<Map<String, dynamic>>;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Subject-wise Progress',
          style: AppTheme.headingSmall.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.2,
          ),
          itemCount: subjects.length,
          itemBuilder: (context, index) {
            final subject = subjects[index];
            return _buildSubjectCard(subject, index);
          },
        ),
      ],
    );
  }

  Widget _buildSubjectCard(Map<String, dynamic> subject, int index) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: subject['color'] as Color,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                subject['name'],
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '${subject['score']}%',
            style: AppTheme.headingLarge.copyWith(
              color: subject['color'] as Color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: subject['progress'] as double,
            backgroundColor: (subject['color'] as Color).withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(subject['color'] as Color),
          ),
        ],
      ),
    ).animate(delay: Duration(milliseconds: 100 * index))
        .fadeIn()
        .scale(begin: const Offset(0.8, 0.8));
  }

  Widget _buildGoalsSection() {
    final goals = _dashboardData['goals'] as List<Map<String, dynamic>>;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Your Goals',
          style: AppTheme.headingSmall.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: goals.length,
          itemBuilder: (context, index) {
            final goal = goals[index];
            return _buildGoalCard(goal, index);
          },
        ),
      ],
    );
  }

  Widget _buildGoalCard(Map<String, dynamic> goal, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                goal['title'],
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                '${goal['current']}/${goal['target']}',
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.textSecondary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: goal['progress'] as double,
            backgroundColor: AppTheme.primaryColor.withOpacity(0.2),
            valueColor: const AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          const SizedBox(height: 4),
          Text(
            '${((goal['progress'] as double) * 100).toInt()}% Complete',
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.textTertiary,
            ),
          ),
        ],
      ),
    ).animate(delay: Duration(milliseconds: 100 * index))
        .fadeIn()
        .slideX(begin: 0.2);
  }

  Widget _buildRecentActivity() {
    final activities = _dashboardData['recentActivity'] as List<Map<String, dynamic>>;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Activity',
          style: AppTheme.headingSmall.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppTheme.borderColor),
          ),
          child: ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: activities.length,
            itemBuilder: (context, index) {
              final activity = activities[index];
              return _buildActivityItem(activity, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildUpcomingEvents() {
    final events = _dashboardData['upcomingEvents'] as List<Map<String, dynamic>>;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Upcoming Events',
          style: AppTheme.headingSmall.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppTheme.borderColor),
          ),
          child: ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: events.length,
            itemBuilder: (context, index) {
              final event = events[index];
              return _buildEventItem(event, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildActivityItem(Map<String, dynamic> activity, int index) {
    IconData icon;
    Color color;
    
    switch (activity['type']) {
      case 'test':
        icon = Icons.quiz;
        color = Colors.blue;
        break;
      case 'study':
        icon = Icons.play_circle;
        color = Colors.green;
        break;
      case 'practice':
        icon = Icons.calculate;
        color = Colors.orange;
        break;
      default:
        icon = Icons.circle;
        color = AppTheme.primaryColor;
    }
    
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(icon, color: color, size: 20),
      ),
      title: Text(
        activity['title'],
        style: AppTheme.bodyMedium.copyWith(
          color: AppTheme.textPrimary,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        _formatActivityTime(activity['date']),
        style: AppTheme.bodySmall.copyWith(
          color: AppTheme.textSecondary,
        ),
      ),
    );
  }

  Widget _buildEventItem(Map<String, dynamic> event, int index) {
    IconData icon;
    Color color;
    
    switch (event['type']) {
      case 'test':
        icon = Icons.quiz;
        color = Colors.red;
        break;
      case 'live':
        icon = Icons.live_tv;
        color = Colors.purple;
        break;
      default:
        icon = Icons.event;
        color = AppTheme.primaryColor;
    }
    
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(icon, color: color, size: 20),
      ),
      title: Text(
        event['title'],
        style: AppTheme.bodyMedium.copyWith(
          color: AppTheme.textPrimary,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        _formatEventTime(event['date']),
        style: AppTheme.bodySmall.copyWith(
          color: AppTheme.textSecondary,
        ),
      ),
    );
  }

  String _formatActivityTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);
    
    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  String _formatEventTime(DateTime time) {
    final now = DateTime.now();
    final difference = time.difference(now);
    
    if (difference.inHours < 24) {
      return 'in ${difference.inHours}h ${difference.inMinutes % 60}m';
    } else {
      return 'in ${difference.inDays}d ${difference.inHours % 24}h';
    }
  }
}

// OnboardAssessmentDashboard Widget (integrated)
class OnboardAssessmentDashboard extends ConsumerWidget {
  const OnboardAssessmentDashboard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dataAsync = ref.watch(onboardDataProvider);

    return dataAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (err, stack) => Center(child: Text('Error: $err')),
      data: (data) {
        if (data.isEmpty) return const Text('No assessment data found - Login again to get Onboard assessment');
        return Column(
          children: [
            // Column Chart for Overview
            SizedBox(
              height: 200,
              child: BarChart(
                BarChartData(
                  barGroups: [
                    BarChartGroupData(x: 0, barRods: [
                      BarChartRodData(toY: (data['total_questions'] ?? 0).toDouble(), color: Colors.blue),
                      BarChartRodData(toY: (data['correct_answers'] ?? 0).toDouble(), color: Colors.green),
                      BarChartRodData(toY: (data['incorrect_answers'] ?? 0).toDouble(), color: Colors.red),
                    ]),
                  ],
                ),
              ),
            ),
            // Bar Chart for Topic Breakdown
            SizedBox(
              height: 200,
              child: BarChart(
                BarChartData(
                  barGroups: [
                    BarChartGroupData(x: 0, barRods: [
                      // Similar for mental_ability and logical_reasoning
                    ]),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}