import 'package:flutter/material.dart';

// Enum for roles based on app_router.dart
enum UserRole {
  student,
  parent,
  director,
  kotaTeacher,
  center<PERSON>ou<PERSON>lor,
  faculty,
  mendor,
}

// Map to define button styles for each role
const _roleButtonStyles = {
  UserRole.student: {
    'backgroundColor': Colors.blue,
    'textColor': Colors.white,
    'borderColor': Colors.blueAccent,
    'fontSize': 16.0,
    'padding': EdgeInsets.symmetric(horizontal: 20.0, vertical: 12.0),
  },
  UserRole.parent: {
    'backgroundColor': Colors.green,
    'textColor': Colors.white,
    'borderColor': Colors.greenAccent,
    'fontSize': 16.0,
    'padding': EdgeInsets.symmetric(horizontal: 20.0, vertical: 12.0),
  },
  UserRole.director: {
    'backgroundColor': Colors.purple,
    'textColor': Colors.white,
    'borderColor': Colors.purpleAccent,
    'fontSize': 18.0,
    'padding': EdgeInsets.symmetric(horizontal: 24.0, vertical: 14.0),
  },
  UserRole.kotaTeacher: {
    'backgroundColor': Colors.orange,
    'textColor': Colors.black,
    'borderColor': Colors.orangeAccent,
    'fontSize': 16.0,
    'padding': EdgeInsets.symmetric(horizontal: 20.0, vertical: 12.0),
  },
  UserRole.centerCounselor: {
    'backgroundColor': Colors.teal,
    'textColor': Colors.white,
    'borderColor': Colors.tealAccent,
    'fontSize': 16.0,
    'padding': EdgeInsets.symmetric(horizontal: 20.0, vertical: 12.0),
  },
  UserRole.faculty: {
    'backgroundColor': Colors.red,
    'textColor': Colors.white,
    'borderColor': Colors.redAccent,
    'fontSize': 16.0,
    'padding': EdgeInsets.symmetric(horizontal: 20.0, vertical: 12.0),
  },
  UserRole.mendor: {
    'backgroundColor': Colors.indigo,
    'textColor': Colors.white,
    'borderColor': Colors.indigoAccent,
    'fontSize': 16.0,
    'padding': EdgeInsets.symmetric(horizontal: 20.0, vertical: 12.0),
  },
};

class CustomButton extends StatelessWidget {
  final String text; // Button text
  final UserRole role; // Role to determine style
  final VoidCallback onPressed; // Action on button press
  final IconData? icon; // Optional icon
  final double? customFontSize; // Override default font size
  final EdgeInsets? customPadding; // Override default padding
  final bool isLoading; // Show loading state
  final bool isDisabled; // Disable button

  const CustomButton({
    required this.text,
    required this.role,
    required this.onPressed,
    this.icon,
    this.customFontSize,
    this.customPadding,
    this.isLoading = false,
    this.isDisabled = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final style = _roleButtonStyles[role] ?? _roleButtonStyles[UserRole.student]!; // Default to student style if role not found

    return ElevatedButton(
      onPressed: isDisabled || isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: style['backgroundColor'] as Color,
        foregroundColor: style['textColor'] as Color,
        padding: customPadding ?? style['padding'] as EdgeInsets,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
          side: BorderSide(color: style['borderColor'] as Color),
        ),
        elevation: 5.0,
      ),
      child: isLoading
          ? const SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 2.0,
              ),
            )
          : Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (icon != null) ...[
                  Icon(icon, color: style['textColor'] as Color),
                  const SizedBox(width: 8.0),
                ],
                Text(
                  text,
                  style: TextStyle(
                    fontSize: customFontSize ?? style['fontSize'] as double,
                    color: style['textColor'] as Color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
    );
  }
}