
class ApiResponse {
  final String subject;
  final String topic;
  final String extractedQuestion;
  final int questionId;
  final List<Question> questions;

  ApiResponse({
    required this.subject,
    required this.topic,
    required this.extractedQuestion,
    required this.questionId,
    required this.questions,
  });

  factory ApiResponse.fromJson(Map<String, dynamic> json) => ApiResponse(
        subject: json['subject']?.toString() ?? '',
        topic: json['topic']?.toString() ?? '',
        extractedQuestion: json['extracted_question']?.toString() ?? '',
        questionId: _intFrom(json['question_id']),
        questions: (json['questions'] as List<dynamic>? ?? const [])
            .map((e) => Question.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  static int _intFrom(dynamic v) {
    if (v is int) return v;
    if (v is String) return int.tryParse(v) ?? 0;
    return 0;
  }
}

class Question {
  final int id;
  final String questionText;
  final Map<String, String> options;
  final String correctAnswer; // key e.g. 'A'
  final String solution;

  Question({
    required this.id,
    required this.questionText,
    required this.options,
    required this.correctAnswer,
    required this.solution,
  });

  factory Question.fromJson(Map<String, dynamic> json) {
    final opts = <String, String>{};
    if (json['options'] is Map) {
      (json['options'] as Map).forEach((k, v) => opts[k.toString()] = v.toString());
    }
    return Question(
      id: ApiResponse._intFrom(json['id']),
      questionText: json['question_text']?.toString() ?? '',
      options: opts,
      correctAnswer: json['correct_answer']?.toString() ?? '',
      solution: json['solution']?.toString() ?? '',
    );
  }
}