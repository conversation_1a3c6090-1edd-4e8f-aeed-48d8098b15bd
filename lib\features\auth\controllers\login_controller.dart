/// login_controller.dart - Login Controller for MVC Architecture
///
/// English: This controller acts as an intermediary between the LoginModel and LoginView.
/// It handles user interactions, coordinates business logic execution, manages navigation,
/// and provides a clean interface for the view to interact with the model. This follows
/// the MVC pattern where the controller manages the flow between model and view.
///
/// Tanglish: LoginModel and LoginView ku naduvula irukka vendiya controller. User interactions
/// handle pannum, business logic execution coordinate pannum, navigation manage pannum,
/// view ku model oda interact panna clean interface provide pannum. MVC pattern follow
/// pannitu controller model and view ku naduvula flow manage pannum.
///
/// Key Responsibilities:
/// - Handle user input and form interactions
/// - Coordinate login business logic execution
/// - Manage navigation flow after authentication
/// - Provide clean interface between model and view
/// - <PERSON>le animation triggers and UI state changes
library;

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../models/login_model.dart';
import '../../../core/models/auth_models.dart';
import '../../../core/utils/logger.dart';

/// LoginController - Controller for Login Flow Management
///
/// English: Controller class that manages the login flow and coordinates between model and view.
/// Tanglish: Login flow manage panna vendiya controller class, model and view ku naduvula coordinate pannum.
class LoginController extends ChangeNotifier {
  final LoginModel _model;
  late final VoidCallback _authServiceListener;
  
  // Animation and UI state
  bool _shouldTriggerShakeAnimation = false;
  
  LoginController(this._model) {
    _authServiceListener = _handleAuthStateChange;
    _model.listenToAuthService(_authServiceListener);
  }
  
  // Getters that delegate to model
  String get username => _model.username;
  String get password => _model.password;
  bool get isLoading => _model.isLoading;
  bool get isPasswordVisible => _model.isPasswordVisible;
  String? get errorMessage => _model.errorMessage;
  String? get usernameError => _model.usernameError;
  String? get passwordError => _model.passwordError;
  bool get isFormValid => _model.isFormValid;
  bool get shouldTriggerShakeAnimation => _shouldTriggerShakeAnimation;
  
  /// Handle username input change
  void onUsernameChanged(String value) {
    _model.setUsername(value);
  }
  
  /// Handle password input change
  void onPasswordChanged(String value) {
    _model.setPassword(value);
  }
  
  /// Handle password visibility toggle
  void onPasswordVisibilityToggle() {
    _model.togglePasswordVisibility();
  }
  
  /// Handle form submission
  Future<void> onLoginSubmit(BuildContext context) async {
    if (!_model.validateForm()) {
      _triggerShakeAnimation();
      return;
    }
    
    // Clear any previous errors
    _model.clearError();
    
    // Add haptic feedback
    HapticFeedback.lightImpact();
    
    final result = await _model.login();
    
    if (!result.success) {
      _triggerShakeAnimation();
      HapticFeedback.heavyImpact();
    }
  }
  
  /// Handle error dismissal
  void onErrorDismiss() {
    _model.clearError();
  }
  
  /// Handle field submission (Enter key)
  void onFieldSubmitted(String value, {bool isPassword = false}) {
    if (isPassword) {
      // If password field, attempt login
      // Note: Context will be passed from view
    } else {
      // If username field, move to password field
      // Focus management will be handled by view
    }
  }
  
  /// Trigger shake animation for errors
  void _triggerShakeAnimation() {
    _shouldTriggerShakeAnimation = true;
    notifyListeners();
    
    // Reset animation trigger after a short delay
    Future.delayed(const Duration(milliseconds: 100), () {
      _shouldTriggerShakeAnimation = false;
      notifyListeners();
    });
  }
  
  /// Handle authentication state changes
  void _handleAuthStateChange() {
    final authState = _model.authState;
    final authError = _model.authServiceError;
    
    // Update loading state based on auth service
    notifyListeners();
    
    AppLogger.auth('Auth state changed in login controller: ${authState.name}');
  }
  
  /// Navigate based on authentication state
  void handleNavigation(BuildContext context) {
    final authState = _model.authState;
    
    switch (authState) {
      case AuthState.otpRequired:
        context.go('/auth/otp');
        break;
      case AuthState.authenticated:
        context.go('/dashboard');
        break;
      case AuthState.error:
        // Error handling is done through model error message
        break;
      default:
        // Stay on login page
        break;
    }
  }
  
  /// Reset controller state
  void reset() {
    _model.reset();
    _shouldTriggerShakeAnimation = false;
    notifyListeners();
  }
  
  /// Check if should show loading state
  bool get shouldShowLoading => _model.isLoading || _model.authState == AuthState.loading;
  
  /// Get effective error message (model error or auth service error)
  String? get effectiveErrorMessage => _model.errorMessage ?? _model.authServiceError;
  
  @override
  void dispose() {
    _model.removeAuthServiceListener(_authServiceListener);
    super.dispose();
  }
}
