import 'package:flutter_test/flutter_test.dart';
import 'package:sasthra_mobile_app/features/auth/models/login_model.dart';

void main() {
  group('LoginModel Tests', () {
    late LoginModel loginModel;

    setUp(() {
      loginModel = LoginModel();
    });

    tearDown(() {
      loginModel.dispose();
    });

    test('should initialize with empty values', () {
      expect(loginModel.username, isEmpty);
      expect(loginModel.password, isEmpty);
      expect(loginModel.isLoading, isFalse);
      expect(loginModel.isPasswordVisible, isFalse);
      expect(loginModel.errorMessage, isNull);
    });

    test('should set username and validate', () {
      loginModel.setUsername('testuser');
      expect(loginModel.username, equals('testuser'));
      expect(loginModel.usernameError, isNull);
    });

    test('should validate empty username', () {
      loginModel.setUsername('');
      expect(loginModel.usernameError, equals('Username is required'));
    });

    test('should set password and validate', () {
      loginModel.setPassword('password123');
      expect(loginModel.password, equals('password123'));
      expect(loginModel.passwordError, isNull);
    });

    test('should validate empty password', () {
      loginModel.setPassword('');
      expect(loginModel.passwordError, equals('Password is required'));
    });

    test('should validate short password', () {
      loginModel.setPassword('123');
      expect(loginModel.passwordError, equals('Password must be at least 6 characters'));
    });

    test('should toggle password visibility', () {
      expect(loginModel.isPasswordVisible, isFalse);
      loginModel.togglePasswordVisibility();
      expect(loginModel.isPasswordVisible, isTrue);
      loginModel.togglePasswordVisibility();
      expect(loginModel.isPasswordVisible, isFalse);
    });

    test('should validate form correctly', () {
      // Invalid form
      expect(loginModel.validateForm(), isFalse);
      expect(loginModel.isFormValid, isFalse);

      // Valid form
      loginModel.setUsername('testuser');
      loginModel.setPassword('password123');
      expect(loginModel.validateForm(), isTrue);
      expect(loginModel.isFormValid, isTrue);
    });

    test('should reset form data', () {
      loginModel.setUsername('testuser');
      loginModel.setPassword('password123');
      loginModel.togglePasswordVisibility();
      
      loginModel.reset();
      
      expect(loginModel.username, isEmpty);
      expect(loginModel.password, isEmpty);
      expect(loginModel.isPasswordVisible, isFalse);
      expect(loginModel.errorMessage, isNull);
      expect(loginModel.usernameError, isNull);
      expect(loginModel.passwordError, isNull);
      expect(loginModel.isLoading, isFalse);
    });

    test('should clear error message', () {
      // Simulate setting an error (this would normally come from auth service)
      loginModel.setUsername(''); // This will set username error
      expect(loginModel.usernameError, isNotNull);
      
      loginModel.clearError();
      // Note: clearError only clears the main error message, not validation errors
      expect(loginModel.errorMessage, isNull);
    });
  });
}
