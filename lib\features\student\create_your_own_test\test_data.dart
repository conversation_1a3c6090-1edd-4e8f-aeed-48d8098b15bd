import 'dart:convert';

class TestData {
  final String examId;
  final String examName;
  final String userId;
  final String subject;
  final String? testDuration;
  final int numQuestions;
  final List<Question> questions;

  TestData({
    required this.examId,
    required this.examName,
    required this.userId,
    required this.subject,
    this.testDuration,
    required this.numQuestions,
    required this.questions,
  });

  factory TestData.fromJson(Map<String, dynamic> json, {required String examName, required String subject}) {
    return TestData(
      examId: json['exam_id'] as String,
      examName: examName,
      userId: json['user_id']?.toString() ?? json['first_name'] + '_' + json['last_name'],
      subject: subject,
      testDuration: json['duration']?.toString() ?? '30 minutes',
      numQuestions: json['num_questions'] as int,
      questions: (json['questions'] as List<dynamic>)
          .map((q) => Question.fromJson(q as Map<String, dynamic>))
          .toList(),
    );
  }
}

class Question {
  final String id;
  final String questionText;
  final List<String> options;
  final String? imageUrl;
  final String correctAnswer;

  Question({
    required this.id,
    required this.questionText,
    required this.options,
    this.imageUrl,
    required this.correctAnswer,
  });

  factory Question.fromJson(Map<String, dynamic> json) {
    return Question(
      id: json['id'] as String,
      questionText: json['question_text'] as String,
      options: (json['options'] as Map<String, dynamic>)
          .values
          .cast<String>()
          .toList(),
      imageUrl: json['image_url'] as String?,
      correctAnswer: json['correct_answer'] as String,
    );
  }
}

class FeedbackData {
  final Map<String, dynamic> scoreSummary;
  final String overallAssessment;
  final String motivationalClosing;
  final List<String> topicStrengths;
  final Map<String, dynamic> aiFeedback;
  final List<Map<String, dynamic>> detailedQuestionFeedback;
  final List<String> generalStudyTips;

  FeedbackData({
    required this.scoreSummary,
    required this.overallAssessment,
    required this.motivationalClosing,
    required this.topicStrengths,
    required this.aiFeedback,
    required this.detailedQuestionFeedback,
    required this.generalStudyTips,
  });

  factory FeedbackData.fromJson(Map<String, dynamic> json) {
    return FeedbackData(
      scoreSummary: json['score_summary'] as Map<String, dynamic>,
      overallAssessment: json['overall_assessment']?.toString() ?? '',
      motivationalClosing: json['motivational_closing']?.toString() ?? '',
      topicStrengths: List<String>.from(json['topic_strengths'] ?? []),
      aiFeedback: json['ai_feedback'] as Map<String, dynamic>? ?? {},
      detailedQuestionFeedback:
          List<Map<String, dynamic>>.from(json['detailed_question_feedback'] ?? []),
      generalStudyTips: List<String>.from(json['general_study_tips'] ?? []),
    );
  }
}