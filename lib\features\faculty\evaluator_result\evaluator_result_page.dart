import 'package:flutter/material.dart';
import '../../../core/widgets/base_page.dart';
import '../../common/presentation/pages/feature_placeholder_page.dart';

class EvaluatorResultPage extends StatelessWidget {
  const EvaluatorResultPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const FeaturePlaceholderPage(
      title: 'Evaluator Result',
      subtitle: 'View evaluation results and analytics',
      breadcrumbs: ['Dashboard', 'Faculty', 'Evaluator Result'],
      featureName: 'Evaluator Result',
    );
  }
}
