import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/widgets/base_page.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/logger.dart';

class MappingCentersPage extends StatefulWidget {
  const MappingCentersPage({super.key});

  @override
  State<MappingCentersPage> createState() => _MappingCentersPageState();
}

class _MappingCentersPageState extends State<MappingCentersPage> {
  bool _isLoading = false;
  List<Map<String, dynamic>> _centers = [];
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadCenters();
  }

  Future<void> _loadCenters() async {
    setState(() => _isLoading = true);
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));
    
    setState(() {
      _centers = [
        {
          'id': '1',
          'name': 'Sasthra Center - Kota',
          'location': 'Kota, Rajasthan',
          'students': 45,
          'status': 'Active',
          'lastSync': '2 hours ago',
        },
        {
          'id': '2',
          'name': 'Sasthra Center - Delhi',
          'location': 'New Delhi',
          'students': 32,
          'status': 'Active',
          'lastSync': '1 hour ago',
        },
        {
          'id': '3',
          'name': 'Sasthra Center - Mumbai',
          'location': 'Mumbai, Maharashtra',
          'students': 28,
          'status': 'Inactive',
          'lastSync': '1 day ago',
        },
        {
          'id': '4',
          'name': 'Sasthra Center - Bangalore',
          'location': 'Bangalore, Karnataka',
          'students': 38,
          'status': 'Active',
          'lastSync': '30 minutes ago',
        },
        {
          'id': '5',
          'name': 'Sasthra Center - Hyderabad',
          'location': 'Hyderabad, Telangana',
          'students': 25,
          'status': 'Active',
          'lastSync': '3 hours ago',
        },
      ];
      _isLoading = false;
    });
    
    AppLogger.userAction('Mapping Centers loaded');
  }

  List<Map<String, dynamic>> get _filteredCenters {
    if (_searchQuery.isEmpty) return _centers;
    
    return _centers.where((center) {
      return center['name'].toLowerCase().contains(_searchQuery.toLowerCase()) ||
             center['location'].toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return BasePage(
      title: 'Mapping Centers',
      subtitle: 'Manage your assigned centers',
      breadcrumbs: const ['Dashboard', 'Kota Teacher', 'Mapping Centers'],
      isLoading: _isLoading,
      actions: [
        IconButton(
          onPressed: _loadCenters,
          icon: const Icon(Icons.refresh),
          tooltip: 'Refresh',
        ),
      ],
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddCenterDialog,
        icon: const Icon(Icons.add),
        label: const Text('Add Center'),
        backgroundColor: AppTheme.primaryColor,
      ),
      child: _buildCentersContent(),
    );
  }

  Widget _buildCentersContent() {
    return Column(
      children: [
        _buildSearchBar(),
        const SizedBox(height: 16),
        _buildStatsRow(),
        const SizedBox(height: 20),
        Expanded(
          child: _filteredCenters.isEmpty
              ? _buildEmptyState()
              : _buildCentersList(),
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: TextField(
        onChanged: (value) => setState(() => _searchQuery = value),
        decoration: InputDecoration(
          hintText: 'Search centers...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: () => setState(() => _searchQuery = ''),
                  icon: const Icon(Icons.clear),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: AppTheme.borderColor),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: AppTheme.borderColor),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: AppTheme.primaryColor, width: 2),
          ),
          filled: true,
          fillColor: AppTheme.surfaceColor,
        ),
      ).animate().fadeIn().slideY(begin: -0.1),
    );
  }

  Widget _buildStatsRow() {
    final totalCenters = _centers.length;
    final activeCenters = _centers.where((c) => c['status'] == 'Active').length;
    final totalStudents = _centers.fold<int>(0, (sum, c) => sum + (c['students'] as int));

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard('Total Centers', totalCenters.toString(), Icons.location_city, Colors.blue),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard('Active Centers', activeCenters.toString(), Icons.check_circle, Colors.green),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard('Total Students', totalStudents.toString(), Icons.people, Colors.orange),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: AppTheme.headingMedium.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ).animate().fadeIn().scale(begin: const Offset(0.8, 0.8));
  }

  Widget _buildCentersList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _filteredCenters.length,
      itemBuilder: (context, index) {
        final center = _filteredCenters[index];
        return _buildCenterCard(center, index);
      },
    );
  }

  Widget _buildCenterCard(Map<String, dynamic> center, int index) {
    final isActive = center['status'] == 'Active';
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: isActive ? Colors.green.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.location_city,
            color: isActive ? Colors.green : Colors.grey,
            size: 24,
          ),
        ),
        title: Text(
          center['name'],
          style: AppTheme.bodyLarge.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.location_on, size: 14, color: AppTheme.textSecondary),
                const SizedBox(width: 4),
                Text(
                  center['location'],
                  style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.people, size: 14, color: AppTheme.textSecondary),
                const SizedBox(width: 4),
                Text(
                  '${center['students']} students',
                  style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
                ),
                const SizedBox(width: 16),
                const Icon(Icons.access_time, size: 14, color: AppTheme.textSecondary),
                const SizedBox(width: 4),
                Text(
                  'Synced ${center['lastSync']}',
                  style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
                ),
              ],
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: isActive ? Colors.green.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                center['status'],
                style: AppTheme.bodySmall.copyWith(
                  color: isActive ? Colors.green : Colors.grey,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(height: 8),
            const Icon(
              Icons.arrow_forward_ios,
              size: 14,
              color: AppTheme.textTertiary,
            ),
          ],
        ),
        onTap: () => _showCenterDetails(center),
      ),
    ).animate(delay: Duration(milliseconds: 100 * index))
        .fadeIn()
        .slideX(begin: 0.2);
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.location_off,
            size: 64,
            color: AppTheme.textTertiary,
          ),
          const SizedBox(height: 16),
          Text(
            'No centers found',
            style: AppTheme.headingSmall.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty
                ? 'Try adjusting your search criteria'
                : 'No centers have been mapped yet',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ).animate().fadeIn().scale(begin: const Offset(0.8, 0.8));
  }

  void _showCenterDetails(Map<String, dynamic> center) {
    AppLogger.userAction('Center details viewed', center['name']);
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: AppTheme.textTertiary,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    center['name'],
                    style: AppTheme.headingMedium.copyWith(
                      color: AppTheme.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    center['location'],
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Center details and management options will be available here.',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddCenterDialog() {
    AppLogger.userAction('Add center dialog opened');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Center'),
        content: const Text('Center addition functionality will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Add center feature coming soon!'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }
}
