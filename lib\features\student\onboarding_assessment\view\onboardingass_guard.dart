import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../view/onboardingass_page.dart';
import '../controller/onboardingass_controller.dart';

class AssessmentGuardWidget extends ConsumerWidget {
  final Widget child;
  const AssessmentGuardWidget({super.key, required this.child});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final statusAsync = ref.watch(checkAssessmentStatusProvider);

    return statusAsync.when(
      loading: () =>
          const Scaffold(body: Center(child: CircularProgressIndicator())),
      error: (err, stack) => Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              const Text('Connection Error',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Text('Error: $err'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.refresh(checkAssessmentStatusProvider),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
      data: (isCompleted) {
        if (!isCompleted) {
          return OnboardingAssessmentPage(onSkip: () {
            ref.read(completeAssessmentProvider.future);
            ref.refresh(checkAssessmentStatusProvider);
          });
        }
        return child;
      },
    );
  }
}
