import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/config/app_config.dart';
import '../model/onboardingass_model.dart';
import '../service/onboardingass.service.dart';

// Dio Provider with Interceptor
final dioProvider = Provider<Dio>((ref) {
  final dio = Dio(BaseOptions(baseUrl: AppConfig.apiBaseUrl));
  dio.interceptors.add(
    InterceptorsWrapper(
      onRequest: (options, handler) async {
        final prefs = await SharedPreferences.getInstance();
        final token = prefs.getString(AppConfig.tokenKey);
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        return handler.next(options);
      },
    ),
  );
  return dio;
});

// Service Provider
final assessmentServiceProvider = Provider<AssessmentService>((ref) {
  final dio = ref.watch(dioProvider);
  return AssessmentService(dio);
});

final startAssessmentProvider = FutureProvider.autoDispose<AssessmentData>((ref) async {
  final service = ref.watch(assessmentServiceProvider);
  return service.startAssessment();
});

final submitAssessmentProvider = FutureProvider.autoDispose.family<void, Map<String, dynamic>>((ref, payload) async {
  final service = ref.watch(assessmentServiceProvider);
  await service.submitAssessment(payload);
});

final checkAssessmentStatusProvider = FutureProvider.autoDispose<bool>((ref) async {
  final service = ref.watch(assessmentServiceProvider);
  return service.checkAssessmentStatus();
});

final completeAssessmentProvider = FutureProvider.autoDispose<void>((ref) async {
  final service = ref.watch(assessmentServiceProvider);
  await service.completeAssessment();
});

final assessmentDataProvider = StateProvider<AssessmentData?>((ref) => null);
final assessmentStatusProvider = StateProvider<bool>((ref) => false);

final onboardDataProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final service = ref.watch(assessmentServiceProvider);
  return service.fetchOnboardData();
});
