// lib/features/student/dashboard/student_dashboard_controller.dart
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/config/app_config.dart';
import '../model/dashboard_model.dart';

class StudentDashboardController extends ChangeNotifier {
  StudentDashboardData? _dashboardData;
  bool _isLoading = false;
  String? _error;
  bool _isAssessmentRequired = false;
  bool _isAssessmentCompleted = false;
  bool _isSkipped = false;

  StudentDashboardData? get dashboardData => _dashboardData;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAssessmentRequired => _isAssessmentRequired;
  bool get isAssessmentCompleted => _isAssessmentCompleted;
  bool get isSkipped => _isSkipped;

  StudentDashboardController() {
    print('[INIT] StudentDashboardController initialized');
    _loadSkipStatus();
  }

  /// Fetch student dashboard data from `/student-dashboard` endpoint
  Future<void> fetchDashboardData() async {
    print('\n===== FETCHING /student-dashboard DATA =====');
    try {
      final studentId = await _getUserId();
      print('[FETCH] Student ID: $studentId');

      _isLoading = true;
      _error = null;
      notifyListeners();

      final token = await _getToken();
      final url = Uri.parse('${AppConfig.apiBaseUrl}${AppConfig.studentDashboardEndpoint}');
      print('[FETCH] API URL: $url');
      print('[FETCH] Authorization Token: $token');

      final response = await http.get(
        url,
        headers: {
          ...AppConfig.defaultHeaders,
          'Authorization': 'Bearer $token',
        },
      ).timeout(AppConfig.apiTimeout);

      print('[FETCH] Status Code: ${response.statusCode}');
      print('[FETCH] Raw Response Body:\n${response.body}');

      if (response.statusCode == 200) {
        final decoded = jsonDecode(response.body);
        print('[FETCH] JSON Decoded Successfully. Top-level Keys: ${decoded.keys}');

        _dashboardData = StudentDashboardData.fromJson(decoded);
        print('[FETCH] Parsed Student Name: '
            '${_dashboardData?.student.firstName} ${_dashboardData?.student.lastName}');
        print('[FETCH] Parsed Course: ${_dashboardData?.student.course}');
        print('[FETCH] Faculty Count: ${_dashboardData?.faculty.length}');
        print('[FETCH] Kota Teachers Count: ${_dashboardData?.kotaTeachers.length}');

        print('✅ /student-dashboard data fetch successful');
      } else {
        _error = 'Failed to load dashboard data: ${response.statusCode}';
        print('❌ $_error');
      }
    } catch (e) {
      _error = 'Error fetching dashboard: $e';
      print('❌ [EXCEPTION] $_error');
    } finally {
      _isLoading = false;
      notifyListeners();
      print('===== /student-dashboard FETCH COMPLETE =====\n');
    }
  }

  /// Check assessment status from API
  Future<void> checkAssessmentStatus() async {
    print('\n===== CHECKING ASSESSMENT STATUS =====');
    try {
      final studentId = await _getUserId();
      print('[ASSESSMENT] Student ID: $studentId');

      final token = await _getToken();
      final url = Uri.parse('${AppConfig.apiBaseUrl}${AppConfig.checkStudentAssessmentEndpoint}');
      print('[ASSESSMENT] API URL: $url');

      final response = await http.post(
        url,
        headers: {
          ...AppConfig.defaultHeaders,
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({'student_id': studentId}),
      ).timeout(AppConfig.apiTimeout);

      print('[ASSESSMENT] Status Code: ${response.statusCode}');
      print('[ASSESSMENT] Raw Response Body:\n${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _isAssessmentRequired = data['assessment_required'] ?? false;
        _isAssessmentCompleted = data['assessment_completed'] ?? false;
        print('[ASSESSMENT] Required: $_isAssessmentRequired, Completed: $_isAssessmentCompleted');

        if (_isAssessmentRequired && !_isAssessmentCompleted && !_isSkipped) {
          _isSkipped = false;
          print('[ASSESSMENT] Reset skip to false');
        }

        print('✅ Assessment status check successful');
        notifyListeners();
      } else {
        _error = 'Failed to check assessment status: ${response.statusCode}';
        print('❌ $_error');
        notifyListeners();
      }
    } catch (e) {
      _error = 'Error checking assessment: $e';
      print('❌ [EXCEPTION] $_error');
      notifyListeners();
    }
    print('===== ASSESSMENT STATUS CHECK COMPLETE =====\n');
  }

  /// Skip assessment and store status in SharedPreferences
  void skipAssessment() async {
    print('[SKIP] Skipping assessment...');
    _isSkipped = true;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isSkip', true);
    print('[SKIP] Skip status saved: $_isSkipped');
    notifyListeners();
  }

  /// Load skip status on initialization
  Future<void> _loadSkipStatus() async {
    final prefs = await SharedPreferences.getInstance();
    _isSkipped = prefs.getBool('isSkip') ?? false;
    print('[INIT] Loaded skip status: $_isSkipped');
    notifyListeners();
  }

  /// Retrieve stored token
  Future<String> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(AppConfig.tokenKey) ?? '';
    print('[TOKEN] Retrieved token: $token');
    return token;
  }

  /// Retrieve stored user ID
  Future<String> _getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    final userDataJson = prefs.getString('user_data');
    if (userDataJson == null || userDataJson.isEmpty) {
      print('❌ [ERROR] User data not found in SharedPreferences');
      throw Exception('User data not found in SharedPreferences');
    }

    final userMap = jsonDecode(userDataJson) as Map<String, dynamic>;
    final userId = userMap['id'] as String?;
    if (userId == null || userId.isEmpty) {
      print('❌ [ERROR] User ID not found in SharedPreferences');
      throw Exception('User ID not found in SharedPreferences');
    }

    print('[USER] Retrieved User ID: $userId');
    return userId;
  }
}
