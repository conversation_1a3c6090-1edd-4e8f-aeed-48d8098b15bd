/// login_model.dart - Login Model for MVC Architecture
///
/// English: This model handles the business logic and data management for login functionality.
/// It manages form validation, user credentials, loading states, and error handling separate
/// from the UI components. This follows the MVC pattern where the model is responsible for
/// data and business logic.
///
/// Tanglish: Login functionality ku vendiya business logic and data management handle pannum
/// model. Form validation, user credentials, loading states, error handling - ellam UI la
/// irundhu separate aaga inga handle pannum. MVC pattern follow pannitu model data and
/// business logic ku responsible.
///
/// Key Responsibilities:
/// - Form validation and credential management
/// - Loading state management
/// - Error handling and message management
/// - Integration with authentication service
/// - State change notifications
library;

import 'package:flutter/foundation.dart';
import '../../../core/services/auth_service.dart';
import '../../../core/models/auth_models.dart';
import '../../../core/utils/logger.dart';

/// LoginModel - Business Logic and Data Management for Login
///
/// English: Model class that handles all login-related business logic and data management.
/// Tanglish: Login related business logic and data management handle panna vendiya model class.
class LoginModel extends ChangeNotifier {
  final AuthService _authService = AuthService();
  
  // Form data
  String _username = '';
  String _password = '';
  
  // State management
  bool _isLoading = false;
  bool _isPasswordVisible = false;
  String? _errorMessage;
  
  // Form validation
  String? _usernameError;
  String? _passwordError;
  
  // Getters
  String get username => _username;
  String get password => _password;
  bool get isLoading => _isLoading;
  bool get isPasswordVisible => _isPasswordVisible;
  String? get errorMessage => _errorMessage;
  String? get usernameError => _usernameError;
  String? get passwordError => _passwordError;
  bool get isFormValid => _usernameError == null && _passwordError == null && 
                         _username.isNotEmpty && _password.isNotEmpty;
  
  /// Set username and validate
  void setUsername(String value) {
    _username = value.trim();
    _validateUsername();
    notifyListeners();
  }
  
  /// Set password and validate
  void setPassword(String value) {
    _password = value;
    _validatePassword();
    notifyListeners();
  }
  
  /// Toggle password visibility
  void togglePasswordVisibility() {
    _isPasswordVisible = !_isPasswordVisible;
    notifyListeners();
  }
  
  /// Validate username
  void _validateUsername() {
    if (_username.isEmpty) {
      _usernameError = 'Username is required';
    } else {
      _usernameError = null;
    }
  }
  
  /// Validate password
  void _validatePassword() {
    if (_password.isEmpty) {
      _passwordError = 'Password is required';
    } else if (_password.length < 6) {
      _passwordError = 'Password must be at least 6 characters';
    } else {
      _passwordError = null;
    }
  }
  
  /// Validate entire form
  bool validateForm() {
    _validateUsername();
    _validatePassword();
    notifyListeners();
    return isFormValid;
  }
  
  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
  
  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  /// Set error message
  void _setError(String? message) {
    _errorMessage = message;
    notifyListeners();
  }
  
  /// Perform login operation
  Future<AuthResult> login() async {
    if (!validateForm()) {
      return AuthResult.failure(message: 'Please fix form errors');
    }
    
    try {
      _setLoading(true);
      _setError(null);
      
      AppLogger.userAction('Login attempt', {'username': _username});
      
      final result = await _authService.login(_username, _password);
      
      if (!result.success && result.message != null) {
        _setError(result.message);
      }
      
      return result;
    } catch (e) {
      final message = 'Login failed: ${e.toString()}';
      _setError(message);
      AppLogger.error(message);
      return AuthResult.failure(message: message, error: Exception(e.toString()));
    } finally {
      _setLoading(false);
    }
  }
  
  /// Reset form data
  void reset() {
    _username = '';
    _password = '';
    _isPasswordVisible = false;
    _errorMessage = null;
    _usernameError = null;
    _passwordError = null;
    _isLoading = false;
    notifyListeners();
  }
  
  /// Listen to auth service state changes
  void listenToAuthService(VoidCallback onStateChange) {
    _authService.addListener(onStateChange);
  }
  
  /// Remove auth service listener
  void removeAuthServiceListener(VoidCallback onStateChange) {
    _authService.removeListener(onStateChange);
  }
  
  /// Get current auth state
  AuthState get authState => _authService.state;
  
  /// Get auth service error message
  String? get authServiceError => _authService.errorMessage;
}
