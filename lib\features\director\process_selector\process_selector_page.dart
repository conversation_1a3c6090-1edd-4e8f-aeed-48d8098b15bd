import 'package:flutter/material.dart';
import '../../../core/widgets/base_page.dart';
import '../../common/presentation/pages/feature_placeholder_page.dart';

class ProcessSelectorPage extends StatelessWidget {
  const ProcessSelectorPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const FeaturePlaceholderPage(
      title: 'Process Selector',
      subtitle: 'Configure system processes',
      breadcrumbs: ['Dashboard', 'Director', 'Process Selector'],
      featureName: 'Process Selector',
    );
  }
}
