import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import './DynamicTestDisplay.dart';
import './unit_and_subtopic_selection.dart'; // Import to access isGeneratingTestProvider

class TestDisplayWrapper extends ConsumerWidget {
  const TestDisplayWrapper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final testData = ref.watch(testProvider);
    final isGeneratingTest = ref.watch(isGeneratingTestProvider);

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFFF5F5F5), Color(0xFF6366F1)],
          ),
        ),
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          child: isGeneratingTest || testData == null
              ? Animate(
                  key: const ValueKey('loading'),
                  effects: const [FadeEffect()],
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 64,
                          height: 64,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.fromBorderSide(
                              BorderSide(color: Colors.transparent, width: 4),
                            ),
                          ),
                          child: Animate(
                            effects: const [
                              RotateEffect(
                                duration: Duration(milliseconds: 1500),
                                curve: Curves.linear,
                              ),
                            ],
                            onPlay: (controller) => controller.repeat(),
                          ),
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Preparing Your Test...',
                          style: TextStyle(fontSize: 20, color: Colors.grey),
                        ),
                      ],
                    ),
                  ),
                )
              : const DynamicTestDisplay(),
        ),
      ),
    );
  }
}