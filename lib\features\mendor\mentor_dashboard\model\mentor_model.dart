import 'dart:convert';
import 'package:http/http.dart' as http;

const String apiBaseUrl = 'https://testing.sasthra.in';

class MentorModel {
  Future<Map<String, dynamic>> getDirectorMentorDashboard(String token) async {
    print('Fetching mentor dashboard data with token: $token');
    try {
      final response = await http.get(
        Uri.parse('$apiBaseUrl/mentor-dashboard'),
        headers: {'Authorization': 'Bearer $token'},
      );
      print('Mentor dashboard response: ${response.statusCode}');
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        print('Mentor dashboard data: $data');
        return data;
      } else {
        print('Failed to load dashboard: ${response.statusCode} - ${response.body}');
        throw Exception('Failed to load dashboard: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching mentor dashboard: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> mentorSession(String action, String token) async {
    print('Updating mentor session: action=$action');
    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/mentor-session'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({'action': action}),
      );
      print('Mentor session response: ${response.statusCode}');
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        print('Mentor session data: $data');
        return data;
      } else {
        print('Failed to update session: ${response.statusCode} - ${response.body}');
        throw Exception('Failed to update session: ${response.statusCode}');
      }
    } catch (e) {
      print('Error updating mentor session: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> pollForCalls(String mentorId, String token) async {
    print('Polling for calls: mentorId=$mentorId');
    try {
      final response = await http.get(
        Uri.parse('$apiBaseUrl/mentor/poll?mentor_id=$mentorId'),
        headers: {'Authorization': 'Bearer $token'},
      );
      print('Poll response: ${response.statusCode}');
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        print('Poll data: $data');
        return data;
      } else {
        print('Failed to poll for calls: ${response.statusCode} - ${response.body}');
        throw Exception('Failed to poll for calls: ${response.statusCode}');
      }
    } catch (e) {
      print('Error polling for calls: $e');
      rethrow;
    }
  }

  Future<void> acceptCall(String mentorId, String roomName, String token) async {
    print('Accepting call: mentorId=$mentorId, roomName=$roomName');
    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/call/accept'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({'mentor_id': mentorId, 'room_name': roomName}),
      );
      print('Accept call response: ${response.statusCode}');
      if (response.statusCode != 200) {
        print('Failed to accept call: ${response.statusCode} - ${response.body}');
        throw Exception('Failed to accept call: ${response.statusCode}');
      }
    } catch (e) {
      print('Error accepting call: $e');
      rethrow;
    }
  }

  Future<void> endCall(String roomName, String token) async {
    print('Ending call: roomName=$roomName');
    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/call/end'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({'room_name': roomName}),
      );
      print('End call response: ${response.statusCode}');
      if (response.statusCode != 200) {
        print('Failed to end call: ${response.statusCode} - ${response.body}');
        throw Exception('Failed to end call: ${response.statusCode}');
      }
    } catch (e) {
      print('Error ending call: $e');
      rethrow;
    }
  }
}