class AssessmentData {
  final String assessmentId;
  final List<Question> questions;

  AssessmentData({required this.assessmentId, required this.questions});

  factory AssessmentData.fromJson(Map<String, dynamic> json) {
    final sections = json['sections'] as List<dynamic>;
    final flatQuestions = sections
        .expand((section) => (section['questions'] as List<dynamic>)
            .map((q) => Question.fromJson(q, section['type'])))
        .toList();
    return AssessmentData(
      assessmentId: json['assessment_id'],
      questions: flatQuestions,
    );
  }
}

class Question {
  final int questionNumber;
  final String questionText;
  final Map<String, String> options;
  final String? imageBase64;
  final String sectionType;

  Question({
    required this.questionNumber,
    required this.questionText,
    required this.options,
    required this.imageBase64,
    required this.sectionType,
  });

  factory Question.fromJson(Map<String, dynamic> json, String sectionType) {
    return Question(
      questionNumber: json['question_number'],
      questionText: json['question_text'],
      options: Map<String, String>.from(json['options']),
      imageBase64: json['image_base64'],
      sectionType: sectionType,
    );
  }
}
