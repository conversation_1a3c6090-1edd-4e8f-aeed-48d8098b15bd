import 'package:flutter/material.dart';
import 'exam_and_subject_selection.dart';
import 'unit_and_subtopic_selection.dart';
import 'test_display_wrapper.dart';

class CreateYourOwnTestParent extends StatefulWidget {
  const CreateYourOwnTestParent({super.key});

  @override
  State<CreateYourOwnTestParent> createState() => _CreateYourOwnTestParentState();
}

class _CreateYourOwnTestParentState extends State<CreateYourOwnTestParent> {
  String _selectedExam = '';
  String _selectedModule = '';
  List<String> _selectedUnits = [];
  List<String> _selectedSubtopics = [];
  bool _testStarted = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 600),
        child: _buildContent(),
      ),
    );
  }

  Widget _buildContent() {
    if (_selectedModule.isEmpty) {
      return ExamAndSubjectSelection(
        selectedExam: _selectedExam,
        selectedModule: _selectedModule,
        setSelectedExam: (value) => setState(() => _selectedExam = value),
        setSelectedModule: (value) => setState(() => _selectedModule = value),
      );
    } else if (!_testStarted) {
      return UnitAndSubtopicSelection(
        selectedExam: _selectedExam,
        selectedModule: _selectedModule,
        selectedUnits: _selectedUnits,
        selectedSubtopics: _selectedSubtopics,
        setSelectedUnits: (value) => setState(() => _selectedUnits = value),
        setSelectedSubtopics: (value) => setState(() => _selectedSubtopics = value),
        setTestStarted: (value) => setState(() => _testStarted = value),
      );
    } else {
      return const TestDisplayWrapper();
    }
  }
}