/// otp_page.dart - OTP Verification Page (MVC View)
///
/// English: This page is the View component in the MVC architecture for OTP verification.
/// It provides a secure OTP input interface where users enter the 6-digit verification code
/// to complete authentication. The page focuses purely on UI rendering and user interactions,
/// delegating all business logic to the OtpController. It includes OTP input fields, countdown
/// timer display, resend functionality, and smooth animations for enhanced user experience.
///
/// Tanglish: Inga OTP verification ku page irukku, MVC architecture la View component.
/// 6-digit verification code enter pannitu authentication complete panna vendiya page.
/// UI rendering and user interactions ku focus pannum, business logic ellam OtpController
/// ku delegate pannum. OTP input fields, countdown timer, resend functionality - ellam irukku.
///
/// Key Features:
/// - Pure View component following MVC pattern
/// - 6-digit OTP input with automatic field navigation
/// - Countdown timer display for resend functionality
/// - Real-time validation error display
/// - Loading states during verification process
/// - Smooth animations and transitions
/// - Error handling with user-friendly messages
/// - Responsive design for different screen sizes
///
/// MVC Architecture:
/// - View: Handles UI rendering and user interactions
/// - Controller: Manages business logic and coordinates with model
/// - Model: Handles OTP validation and verification operations
/// otp_page.dart - Enhanced OTP UI (Matches First Login/Forgot UI, No Gradients)
///
/// - Solid brand header with rounded bottom and circular logo (same as first login UI)
/// - Clean card layout for OTP inputs, error, actions
/// - Solid brand button (no gradients)
/// - Responsive sizing using MediaQuery/LayoutBuilder to look good on all mobile sizes
/// - Keeps MVC wiring: OtpController/OtpModel methods and state handling
library;

import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_theme.dart';
import '../../models/otp_model.dart';
import '../../controllers/otp_controller.dart';

class OtpPage extends StatefulWidget {
  const OtpPage({super.key});

  @override
  State<OtpPage> createState() => _OtpPageState();
}

class _OtpPageState extends State<OtpPage> with TickerProviderStateMixin {
  // Brand palette to match first UI (login/forgot)
  static const kBrandPrimary = Color(0xFF1E88E5); // Blue
  static const kBrandAccent = Color(0xFF4CAF50);  // Green
  static const kBackground = Color(0xFFF6F8FB);  // Neutral soft bg
  static const kSurface = Colors.white;

  final List<TextEditingController> _otpControllers = List.generate(6, (_) => TextEditingController());
  final List<FocusNode> _otpFocusNodes = List.generate(6, (_) => FocusNode());

  // MVC Components
  late final OtpModel _model;
  late final OtpController _controller;

  late AnimationController _slideController;
  late AnimationController _shakeController;
  late AnimationController _fadeController;

  @override
  void initState() {
    super.initState();
    _initializeMVC();
    _initializeAnimations();
    _setupControllerListeners();

    // Match header area with status bar style (brand color behind status icons)
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: kBrandPrimary,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark,
      ),
    );
  }

  void _initializeMVC() {
    _model = OtpModel();
    _controller = OtpController(_model);
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 450),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _slideController.forward();
    Future.delayed(const Duration(milliseconds: 150), () {
      if (mounted) _fadeController.forward();
    });
  }

  void _setupControllerListeners() {
    _controller.addListener(() {
      if (!mounted) return;
      setState(() {});
      _controller.handleNavigation(context);
      if (_controller.shouldTriggerShakeAnimation) {
        _shakeController.forward().then((_) => _shakeController.reset());
      }
    });
  }

  void _clearOtpFields() {
    _controller.clearOtpFields();
    _otpFocusNodes.first.requestFocus();
  }

  Future<void> _handleOtpVerification() async {
    await _controller.onOtpSubmit();
  }

  Future<void> _handleResendOtp() async {
    await _controller.onResendOtp();

    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('OTP resent successfully'),
        backgroundColor: AppTheme.successColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  @override
  void dispose() {
    _slideController.dispose();
    _shakeController.dispose();
    _fadeController.dispose();
    for (var c in _otpControllers) {
      c.dispose();
    }
    for (var f in _otpFocusNodes) {
      f.dispose();
    }
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final mq = MediaQuery.of(context);
    final width = mq.size.width;
    final isTablet = width >= 600;
    final horizontalPad = width < 360
        ? 12.0
        : width < 400
            ? 16.0
            : 20.0;
    final cardMaxWidth = isTablet ? 560.0 : math.min(480.0, width - (horizontalPad * 2));
    final taglineSize = isTablet ? 16.0 : 14.0;

    return Scaffold(
      backgroundColor: kBackground,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.only(bottom: math.max(16.0, mq.viewInsets.bottom + 16.0)),
          child: Column(
            children: [
              _buildHeader(), // solid brand header like login/forgot
              const SizedBox(height: 16),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: horizontalPad),
                child: Center(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: cardMaxWidth),
                    child: _buildOtpCard(taglineSize),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  // Header: brand primary with rounded bottom and circular white logo
  Widget _buildHeader() {
    return AnimatedBuilder(
      animation: _slideController,
      builder: (context, child) {
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.fromLTRB(20, 28, 20, 28),
          decoration: const BoxDecoration(
            color: kBrandPrimary,
            borderRadius: BorderRadius.vertical(bottom: Radius.circular(24)),
          ),
          child: Transform.translate(
            offset: Offset(0, 40 * (1 - _slideController.value)),
            child: Opacity(
              opacity: _slideController.value,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: 96,
                    height: 96,
                    decoration: const BoxDecoration(
                      color: kSurface,
                      shape: BoxShape.circle,
                    ),
                    alignment: Alignment.center,
                    child: Image.asset(
                      'assets/icons/sasthra_logo.png',
                      width: 56,
                      height: 56,
                      fit: BoxFit.contain,
                      errorBuilder: (_, __, ___) => const Icon(
                        Icons.sms_outlined,
                        size: 40,
                        color: kBrandPrimary,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Verify OTP',
                    textAlign: TextAlign.center,
                    style: AppTheme.headingLarge.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    'Enter the 6-digit code sent to your\nregistered mobile number',
                    textAlign: TextAlign.center,
                    style: AppTheme.bodyMedium.copyWith(
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildOtpCard(double taglineSize) {
    return AnimatedBuilder(
      animation: _fadeController,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeController.value,
          child: Card(
            elevation: 2,
            color: kSurface,
            shadowColor: Colors.black.withOpacity(0.06),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(color: Colors.black.withOpacity(0.06)),
            ),
            child: Padding(
              padding: const EdgeInsets.fromLTRB(20, 20, 20, 16),
              child: Column(
                children: [
                  // Error message (if any) with subtle animation
                  if (_controller.effectiveErrorMessage != null)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: AppTheme.errorColor.withOpacity(0.08),
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(color: AppTheme.errorColor.withOpacity(0.3)),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Icon(Icons.error_outline, color: AppTheme.errorColor, size: 20),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _controller.effectiveErrorMessage!,
                              style: AppTheme.bodySmall.copyWith(color: AppTheme.errorColor),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.close, size: 18),
                            color: AppTheme.errorColor,
                            onPressed: _controller.onErrorDismiss,
                          ),
                        ],
                      ),
                    ).animate().fadeIn().slideY(begin: -0.2, end: 0.0),

                  // OTP inputs
                  _buildOtpRow(),

                  const SizedBox(height: 20),

                  // Verify button
                  SizedBox(
                    width: double.infinity,
                    height: 52,
                    child: ElevatedButton(
                      onPressed: _controller.shouldShowLoading || !_controller.isOtpValid
                          ? null
                          : _handleOtpVerification,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: kBrandPrimary,
                        disabledBackgroundColor: kBrandPrimary.withOpacity(0.6),
                        foregroundColor: Colors.white,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: _controller.shouldShowLoading
                          ? const SizedBox(
                              width: 22,
                              height: 22,
                              child: CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                strokeWidth: 2,
                              ),
                            )
                          : Text(
                              'Verify OTP',
                              style: AppTheme.labelLarge.copyWith(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Resend section
                  _buildResendSection(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildOtpRow() {
    final width = MediaQuery.of(context).size.width;
    // Responsive OTP field sizing
    final fieldWidth = width < 340 ? 44.0 : width < 400 ? 48.0 : width < 480 ? 52.0 : 56.0;
    final fieldHeight = fieldWidth + 8;

    return AnimatedBuilder(
      animation: _shakeController,
      builder: (context, child) {
        final shakeOffset = 10 * _shakeController.value * (1 - _shakeController.value) * 4;
        return Transform.translate(
          offset: Offset(shakeOffset, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: List.generate(6, (index) {
              final isHighlighted = _controller.isOtpFieldHighlighted(index);
              return SizedBox(
                width: fieldWidth,
                height: fieldHeight,
                child: _buildOtpField(index, isHighlighted),
              ).animate(delay: Duration(milliseconds: 60 * index)).fadeIn().slideY(begin: 0.25);
            }),
          ),
        );
      },
    );
  }

  Widget _buildOtpField(int index, bool isHighlighted) {
    return DecoratedBox(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: isHighlighted ? kBrandPrimary.withOpacity(0.06) : kSurface,
        border: Border.all(
          color: isHighlighted ? kBrandPrimary : AppTheme.borderColor,
          width: 2,
        ),
      ),
      child: TextFormField(
        controller: _otpControllers[index],
        focusNode: _otpFocusNodes[index],
        textAlign: TextAlign.center,
        keyboardType: TextInputType.number,
        maxLength: 1,
        style: AppTheme.headingSmall.copyWith(
          color: AppTheme.textOtp,
          fontWeight: FontWeight.bold,
          fontSize: 22,
        ),
        decoration: const InputDecoration(
          border: InputBorder.none,
          counterText: '',
          contentPadding: EdgeInsets.only(top: 12),
          isDense: true,
        ),
        inputFormatters: [FilteringTextInputFormatter.digitsOnly],
        onChanged: (value) {
          _controller.onOtpDigitChanged(index, value, _otpFocusNodes);
          setState(() {}); // refresh highlight
        },
      ),
    );
  }

  Widget _buildResendSection() {
    return Column(
      children: [
        Text(
          'Didn\'t receive the code?',
          style: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
        ),
        const SizedBox(height: 8),
        if (!_controller.canResend)
          Text(
            _controller.resendCountdownText,
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.textTertiary),
          )
        else
          TextButton(
            onPressed: _handleResendOtp,
            style: TextButton.styleFrom(foregroundColor: kBrandPrimary),
            child: Text(
              'Resend OTP',
              style: AppTheme.labelLarge.copyWith(
                color: kBrandPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        const SizedBox(height: 8),
        // Back to login for convenience
        TextButton(
          onPressed: () => context.go('/auth/login'),
          child: Text(
            'Back to Login',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
              
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFooter() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Text(
        '© 2025 Sasthra. All rights reserved.',
        textAlign: TextAlign.center,
        style: AppTheme.bodySmall.copyWith(color: AppTheme.textTertiary),
      ),
    );
  }
}