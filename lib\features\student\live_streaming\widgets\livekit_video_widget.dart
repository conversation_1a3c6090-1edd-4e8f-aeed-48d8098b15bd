import 'dart:async';
import 'package:flutter/material.dart';
import 'package:livekit_client/livekit_client.dart' as livekit;

class LiveKitVideoWidget extends StatefulWidget {
  final String livekitUrl;
  final String livekitToken;
  final String participantName;
  final bool isTeacher;

  const LiveKitVideoWidget({
    super.key,
    required this.livekitUrl,
    required this.livekitToken,
    required this.participantName,
    required this.isTeacher,
  });

  @override
  State<LiveKitVideoWidget> createState() => _LiveKitVideoWidgetState();
}

class _LiveKitVideoWidgetState extends State<LiveKitVideoWidget>
    with WidgetsBindingObserver, AutomaticKeepAliveClientMixin {
  livekit.Room? _room;
  bool _isConnecting = true;
  bool _isConnected = false;
  String? _errorMessage;
  List<livekit.RemoteParticipant> _remoteParticipants = [];
  livekit.LocalParticipant? _localParticipant;

  // Connection management
  Timer? _reconnectTimer;
  Timer? _healthCheckTimer;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;
  static const Duration _reconnectDelay = Duration(seconds: 3);

  // Performance optimization
  bool _isInBackground = false;
  bool _isDisposed = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeRoom();
  }

  @override
  void dispose() {
    _isDisposed = true;
    WidgetsBinding.instance.removeObserver(this);
    _cleanupResources();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        _isInBackground = true;
        _pauseVideoTracks();
        break;
      case AppLifecycleState.resumed:
        _isInBackground = false;
        _resumeVideoTracks();
        break;
      case AppLifecycleState.detached:
        _cleanupResources();
        break;
      case AppLifecycleState.hidden:
        break;
    }
  }

  /// Initialize room with optimized settings for mobile
  Future<void> _initializeRoom() async {
    if (_isDisposed) return;

    try {
      if (mounted) {
        setState(() {
          _isConnecting = true;
          _errorMessage = null;
        });
      }

      await _connectToRoom();
    } catch (error) {
      print('❌ Failed to initialize room: $error');
      if (mounted && !_isDisposed) {
        setState(() {
          _isConnecting = false;
          _isConnected = false;
          _errorMessage = 'Failed to initialize: $error';
        });
      }
    }
  }

  /// Connect to LiveKit room with mobile optimizations
  Future<void> _connectToRoom() async {
    if (_isDisposed) return;

    try {
      // Cleanup existing room if any
      await _cleanupRoom();

      // Create new room instance
      _room = livekit.Room();

      // Set up comprehensive event listeners
      _setupRoomListeners();

      // Mobile-optimized room options
      final roomOptions = livekit.RoomOptions(
        adaptiveStream: true,
        dynacast: true,
        // Reduced quality for mobile performance
        defaultCameraCaptureOptions: const livekit.CameraCaptureOptions(
          maxFrameRate: 24, // Reduced from 30 for better performance
          params: livekit.VideoParametersPresets.h540_169, // Reduced from h720
        ),
        defaultScreenShareCaptureOptions: const livekit.ScreenShareCaptureOptions(
          useiOSBroadcastExtension: true,
          params: livekit.VideoParametersPresets.screenShareH720FPS15, // Reduced quality
        ),
        // Mobile-specific optimizations
        e2eeOptions: null, // Disable encryption for better performance
      );

      // Connect with timeout
      await _room!.connect(
        widget.livekitUrl,
        widget.livekitToken,
        roomOptions: roomOptions,
        fastConnectOptions: livekit.FastConnectOptions(
          microphone: livekit.TrackOption(enabled: false), // Always start muted
          camera: livekit.TrackOption(enabled: false), // Always start with camera off
        ),
      ).timeout(
        const Duration(seconds: 15),
        onTimeout: () => throw TimeoutException('Connection timeout', const Duration(seconds: 15)),
      );

      print('✅ Connected to LiveKit room successfully');

      if (mounted && !_isDisposed) {
        setState(() {
          _isConnecting = false;
          _isConnected = true;
          _localParticipant = _room!.localParticipant;
          _remoteParticipants = _room!.remoteParticipants.values.toList();
          _reconnectAttempts = 0; // Reset on successful connection
        });

        // Start health check
        _startHealthCheck();
      }

    } catch (error) {
      print('❌ Failed to connect to LiveKit room: $error');

      if (mounted && !_isDisposed) {
        setState(() {
          _isConnecting = false;
          _isConnected = false;
          _errorMessage = _getErrorMessage(error);
        });

        // Attempt reconnection if not at max attempts
        _scheduleReconnect();
      }
    }
  }

  /// Setup comprehensive room event listeners
  void _setupRoomListeners() {
    if (_room == null || _isDisposed) return;

    _room!.addListener(_onRoomUpdate);

    // Connection state listeners
    _room!.addListener(() {
      if (_isDisposed || !mounted) return;

      final connectionState = _room!.connectionState;
      print('🔄 Room connection state: $connectionState');

      if (connectionState == livekit.ConnectionState.disconnected) {
        _handleDisconnection();
      } else if (connectionState == livekit.ConnectionState.connected) {
        _handleReconnection();
      }
    });

    // Participant listeners
    _room!.addListener(() {
      if (_isDisposed || !mounted) return;
      _updateParticipants();
    });
  }

  /// Handle room updates with error protection
  void _onRoomUpdate() {
    if (!mounted || _isDisposed || _room == null) return;

    try {
      setState(() {
        _remoteParticipants = _room!.remoteParticipants.values.toList();
        _localParticipant = _room!.localParticipant;
      });
    } catch (e) {
      print('❌ Error updating room state: $e');
    }
  }

  /// Update participants list safely
  void _updateParticipants() {
    if (!mounted || _isDisposed || _room == null) return;

    try {
      final newRemoteParticipants = _room!.remoteParticipants.values.toList();

      if (newRemoteParticipants.length != _remoteParticipants.length) {
        setState(() {
          _remoteParticipants = newRemoteParticipants;
        });
        print('👥 Participants updated: ${_remoteParticipants.length} remote participants');
      }
    } catch (e) {
      print('❌ Error updating participants: $e');
    }
  }

  /// Handle disconnection events
  void _handleDisconnection() {
    if (_isDisposed) return;

    print('🔌 Room disconnected, attempting reconnection...');
    _scheduleReconnect();
  }

  /// Handle successful reconnection
  void _handleReconnection() {
    if (_isDisposed) return;

    print('✅ Room reconnected successfully');
    _reconnectAttempts = 0;

    if (mounted) {
      setState(() {
        _isConnected = true;
        _errorMessage = null;
      });
    }
  }

  /// Schedule reconnection attempt
  void _scheduleReconnect() {
    if (_isDisposed || _reconnectAttempts >= _maxReconnectAttempts) {
      print('❌ Max reconnection attempts reached');
      return;
    }

    _reconnectAttempts++;
    print('🔄 Scheduling reconnection attempt $_reconnectAttempts/$_maxReconnectAttempts');

    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(_reconnectDelay, () {
      if (!_isDisposed && mounted) {
        _connectToRoom();
      }
    });
  }

  /// Start periodic health check
  void _startHealthCheck() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_isDisposed || !mounted) {
        timer.cancel();
        return;
      }

      _performHealthCheck();
    });
  }

  /// Perform connection health check
  void _performHealthCheck() {
    if (_room == null || _isDisposed) return;

    final connectionState = _room!.connectionState;
    if (connectionState != livekit.ConnectionState.connected) {
      print('⚠️ Health check failed: $connectionState');
      _handleDisconnection();
    }
  }

  /// Clean up all resources
  Future<void> _cleanupResources() async {
    _reconnectTimer?.cancel();
    _healthCheckTimer?.cancel();
    await _cleanupRoom();
  }

  /// Clean up room resources
  Future<void> _cleanupRoom() async {
    if (_room != null) {
      try {
        await _room!.disconnect();
        _room!.dispose();
      } catch (e) {
        print('❌ Error cleaning up room: $e');
      } finally {
        _room = null;
      }
    }
  }

  /// Get user-friendly error message
  String _getErrorMessage(dynamic error) {
    if (error is TimeoutException) {
      return 'Connection timeout. Please check your internet connection.';
    } else if (error.toString().contains('token')) {
      return 'Invalid session token. Please try joining again.';
    } else if (error.toString().contains('network') || error.toString().contains('connection')) {
      return 'Network error. Please check your internet connection.';
    } else {
      return 'Connection failed. Please try again.';
    }
  }

  /// Pause video tracks when app goes to background
  void _pauseVideoTracks() {
    if (_room == null || _isDisposed) return;

    try {
      // Pause local video tracks to save battery
      for (final publication in _room!.localParticipant!.videoTrackPublications) {
        if (publication.track != null) {
          publication.track!.disable();
        }
      }

      // Pause remote video tracks to save bandwidth
      for (final participant in _remoteParticipants) {
        for (final publication in participant.videoTrackPublications) {
          if (publication.track != null) {
            publication.track!.disable();
          }
        }
      }

      print('📱 Video tracks paused for background mode');
    } catch (e) {
      print('❌ Error pausing video tracks: $e');
    }
  }

  /// Resume video tracks when app comes to foreground
  void _resumeVideoTracks() {
    if (_room == null || _isDisposed) return;

    try {
      // Resume local video tracks
      for (final publication in _room!.localParticipant!.videoTrackPublications) {
        if (publication.track != null) {
          publication.track!.enable();
        }
      }

      // Resume remote video tracks
      for (final participant in _remoteParticipants) {
        for (final publication in participant.videoTrackPublications) {
          if (publication.track != null) {
            publication.track!.enable();
          }
        }
      }

      print('📱 Video tracks resumed for foreground mode');
    } catch (e) {
      print('❌ Error resuming video tracks: $e');
    }
  }

  Widget _buildParticipantVideo(livekit.Participant participant) {
    final videoTrack = participant.videoTrackPublications
        .where((pub) => pub.subscribed && pub.track != null)
        .map((pub) => pub.track as livekit.VideoTrack)
        .firstOrNull;

    if (videoTrack == null) {
      return Container(
        color: Colors.grey[900],
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: Colors.blue,
                child: Text(
                  (participant.name?.isNotEmpty ?? false)
                      ? participant.name![0].toUpperCase()
                      : '?',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                participant.name ?? 'Unknown',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
              if (participant is livekit.RemoteParticipant)
                const Text(
                  'Camera off',
                  style: TextStyle(
                    color: Colors.white54,
                    fontSize: 12,
                  ),
                ),
            ],
          ),
        ),
      );
    }

    return livekit.VideoTrackRenderer(
      videoTrack,
    );
  }

  Widget _buildConnectionStatus() {
    if (_isConnecting) {
      return Container(
        color: Colors.black,
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: Colors.blue),
              SizedBox(height: 16),
              Text(
                'Connecting to LiveKit...',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ],
          ),
        ),
      );
    }

    if (_errorMessage != null) {
      return Container(
        color: Colors.black,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 64,
              ),
              const SizedBox(height: 16),
              Text(
                'Connection Failed',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _errorMessage!,
                style: const TextStyle(color: Colors.white70, fontSize: 14),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _connectToRoom,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    if (!_isConnected) {
      return _buildConnectionStatus();
    }

    // Show remote participants (teachers/other students)
    if (_remoteParticipants.isNotEmpty) {
      return GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: _remoteParticipants.length == 1 ? 1 : 2,
          childAspectRatio: 16 / 9,
        ),
        itemCount: _remoteParticipants.length,
        itemBuilder: (context, index) {
          final participant = _remoteParticipants[index];
          return Container(
            margin: const EdgeInsets.all(4),
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: _buildParticipantVideo(participant),
                ),
                Positioned(
                  bottom: 8,
                  left: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      participant.name ?? 'Unknown',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      );
    }

    // No remote participants - show waiting message
    return Container(
      color: Colors.black,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              color: Colors.white54,
              size: 64,
            ),
            SizedBox(height: 16),
            Text(
              'Waiting for other participants...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'You are connected to the session',
              style: TextStyle(
                color: Colors.white54,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
