import 'dart:async';
import 'package:flutter/material.dart';
import 'package:livekit_client/livekit_client.dart' as livekit;
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

const String livekitUrl = 'wss://livekit.sasthra.in';
const String apiBaseUrl = 'https://testing.sasthra.in';

class IncomingCallView extends StatefulWidget {
  final String mentorId;
  final Map<String, dynamic>? incomingCall;
  final Function(String, Map<String, dynamic>)? onCallStatusChange;
  final bool showVideoCall;

  const IncomingCallView({
    super.key,
    required this.mentorId,
    this.incomingCall,
    this.onCallStatusChange,
    required this.showVideoCall,
  });

  @override
  State<IncomingCallView> createState() => _IncomingCallViewState();
}

class _IncomingCallViewState extends State<IncomingCallView> {
  String callState = 'idle';
  livekit.Room? room;
  List<livekit.LocalTrack> localTracks = [];
  bool isMuted = false;
  bool isVideoOff = false;
  bool isScreenSharing = false;
  int callDuration = 0;
  String error = '';
  String mediaError = '';
  List<livekit.RemoteParticipant> remoteParticipants = [];
  livekit.LocalVideoTrack? localVideoTrack;
  livekit.LocalAudioTrack? localAudioTrack;
  livekit.RemoteVideoTrack? remoteVideoTrack;
  livekit.RemoteAudioTrack? remoteAudioTrack;
  livekit.RemoteVideoTrack? remoteScreenTrack;
  livekit.RemoteAudioTrack? remoteScreenAudioTrack;
  bool isRemoteAudioMuted = false;

  Timer? callTimer;

  @override
  void initState() {
    super.initState();
    print('Initializing IncomingCallView: showVideoCall=${widget.showVideoCall}, incomingCall=${widget.incomingCall}');
    if (widget.showVideoCall && widget.incomingCall != null) {
      joinRoom(widget.incomingCall!['token'] ?? '');
    }
  }

  @override
  void didUpdateWidget(covariant IncomingCallView oldWidget) {
    super.didUpdateWidget(oldWidget);
    print('IncomingCallView updated: old=${oldWidget.incomingCall}, new=${widget.incomingCall}');
    if (widget.incomingCall != oldWidget.incomingCall && widget.incomingCall != null) {
      final token = widget.incomingCall!['token'] ?? '';
      if (token.isNotEmpty) {
        joinRoom(token);
      } else {
        print('Error: Missing token in incomingCall');
        setState(() {
          error = 'Invalid call token';
          callState = 'error';
        });
        widget.onCallStatusChange?.call('error', {});
      }
    }
  }

  void startCallTimer() {
    print('Starting call timer');
    callTimer?.cancel(); // Cancel any existing timer to prevent leaks
    callTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (mounted) {
        setState(() => callDuration++);
      }
    });
  }

  void stopCallTimer() {
    print('Stopping call timer');
    callTimer?.cancel();
    callTimer = null;
  }

  String formatDuration(int seconds) {
    final mins = seconds ~/ 60;
    final secs = seconds % 60;
    return '${mins.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  Future<void> cleanup() async {
    print('Cleaning up IncomingCallView resources...');
    try {
      if (room != null) {
        await room!.disconnect();
        room = null;
      }
      for (var track in localTracks) {
        await track.stop();
      }
      localTracks.clear();
      localVideoTrack = null;
      localAudioTrack = null;
      remoteParticipants.clear();
      remoteVideoTrack = null;
      remoteAudioTrack = null;
      remoteScreenTrack = null;
      remoteScreenAudioTrack = null;
      isScreenSharing = false;
      isMuted = false;
      isVideoOff = false;
      error = '';
      mediaError = '';
      stopCallTimer();
    } catch (e) {
      print('Error during cleanup: $e');
    }
  }

  Future<void> endCall() async {
    print('Ending call...');
    final roomName = widget.incomingCall?['room_name'];
    if (roomName != null && roomName is String && roomName.isNotEmpty) {
      try {
        final prefs = await SharedPreferences.getInstance();
        final token = prefs.getString('token');
        print('Retrieved token for endCall: $token');
        if (token == null || token.isEmpty) {
          print('Error: No token found for endCall');
          setState(() => error = 'Authentication token missing');
          return;
        }
        final response = await http.post(
          Uri.parse('$apiBaseUrl/call/end'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
          body: jsonEncode({'room_name': roomName}),
        );
        print('End call response: ${response.statusCode}');
        if (response.statusCode != 200) {
          print('Failed to end call: ${response.body}');
          setState(() => error = 'Failed to end call: ${response.statusCode}');
        }
      } catch (e) {
        print('Error ending call: $e');
        setState(() => error = 'Error ending call: $e');
      }
    } else {
      print('Error: Invalid room_name in incomingCall');
      setState(() => error = 'Invalid room name');
    }
    await cleanup();
    if (mounted) {
      setState(() => callState = 'ended');
    }
    widget.onCallStatusChange?.call('ended', widget.incomingCall ?? {});
  }

  Future<void> enableLocalTracks(livekit.Room room) async {
    print('Enabling local tracks...');
    try {
      // Enable camera
      await room.localParticipant?.setCameraEnabled(true);
      print('Camera enabled');

      // Enable microphone
      await room.localParticipant?.setMicrophoneEnabled(true);
      print('Microphone enabled');

      // Get the published tracks
      localVideoTrack = room.localParticipant?.videoTrackPublications
          .where((pub) => pub.track != null)
          .map((pub) => pub.track as livekit.LocalVideoTrack)
          .firstOrNull;

      localAudioTrack = room.localParticipant?.audioTrackPublications
          .where((pub) => pub.track != null)
          .map((pub) => pub.track as livekit.LocalAudioTrack)
          .firstOrNull;

      print('Local tracks enabled: video=${localVideoTrack != null}, audio=${localAudioTrack != null}');
    } catch (e) {
      print('Error enabling local tracks: $e');
      setState(() => mediaError = 'Failed to access camera/microphone: $e');
      setState(() => callState = 'error');
      widget.onCallStatusChange?.call('error', {});
      rethrow;
    }
  }

  void joinRoom(String token) async {
    print('Joining room with token: $token');
    if (token.isEmpty) {
      print('Error: Empty token provided');
      setState(() {
        error = 'Invalid call token';
        callState = 'error';
      });
      widget.onCallStatusChange?.call('error', {});
      return;
    }
    setState(() => callState = 'connecting');
    try {
      final newRoom = livekit.Room();
      // Set up room listeners
      newRoom.addListener(() {
        if (!mounted) return;

        final connectionState = newRoom.connectionState;
        print('Room connection state: $connectionState');

        if (connectionState == livekit.ConnectionState.connected) {
          print('Connected to room');
          setState(() => callState = 'connected');
          startCallTimer();
          enableLocalTracks(newRoom);
          setState(() => remoteParticipants = newRoom.remoteParticipants.values.toList());
          widget.onCallStatusChange?.call('connected', {});
        } else if (connectionState == livekit.ConnectionState.disconnected) {
          print('Disconnected from room');
          endCall();
        }
      });

      // Add a simple listener for participant changes
      newRoom.addListener(() {
        if (!mounted) return;
        setState(() {
          remoteParticipants = newRoom.remoteParticipants.values.toList();

          // Update remote tracks
          for (var participant in remoteParticipants) {
            // Get video track
            final videoTrack = participant.videoTrackPublications
                .where((pub) => pub.subscribed && pub.track != null)
                .map((pub) => pub.track as livekit.RemoteVideoTrack)
                .firstOrNull;
            if (videoTrack != null) {
              remoteVideoTrack = videoTrack;
            }

            // Get audio track
            final audioTrack = participant.audioTrackPublications
                .where((pub) => pub.subscribed && pub.track != null)
                .map((pub) => pub.track as livekit.RemoteAudioTrack)
                .firstOrNull;
            if (audioTrack != null) {
              remoteAudioTrack = audioTrack;
            }
          }
        });
      });

      await newRoom.connect(livekitUrl, token);
      if (mounted) setState(() => room = newRoom);
      print('Room connection initiated');
    } catch (e) {
      print('Error joining room: $e');
      if (mounted) {
        setState(() {
          error = 'Failed to connect: $e';
          callState = 'error';
        });
      }
      widget.onCallStatusChange?.call('error', {});
    }
  }

  Future<void> toggleMute() async {
    print('Toggling mute: current=$isMuted');
    if (localAudioTrack != null) {
      try {
        if (isMuted) {
          await localAudioTrack!.unmute();
        } else {
          await localAudioTrack!.mute();
        }
        if (mounted) setState(() => isMuted = !isMuted);
        print('Mute state changed to: $isMuted');
      } catch (e) {
        print('Error toggling mute: $e');
      }
    }
  }

  Future<void> toggleVideo() async {
    print('Toggling video: current=$isVideoOff');
    if (localVideoTrack != null) {
      try {
        if (isVideoOff) {
          await localVideoTrack!.unmute();
        } else {
          await localVideoTrack!.mute();
        }
        if (mounted) setState(() => isVideoOff = !isVideoOff);
        print('Video state changed to: $isVideoOff');
      } catch (e) {
        print('Error toggling video: $e');
      }
    }
  }

  Future<void> toggleScreenShare() async {
    print('Toggling screen share: current=$isScreenSharing');
    if (room == null) {
      print('Error: No room available for screen share');
      return;
    }
    try {
      if (isScreenSharing) {
        await room?.localParticipant?.setScreenShareEnabled(false);
        if (mounted) setState(() => isScreenSharing = false);
        print('Screen share disabled');
      } else {
        await room?.localParticipant?.setScreenShareEnabled(true);
        if (mounted) setState(() => isScreenSharing = true);
        print('Screen share enabled');
      }
    } catch (e) {
      print('Error toggling screen share: $e');
    }
  }

  @override
  void dispose() {
    print('Disposing IncomingCallView...');
    cleanup();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print('Building IncomingCallView: callState=$callState');
    if (!widget.showVideoCall || widget.incomingCall == null) {
      print('Hiding IncomingCallView: showVideoCall=${widget.showVideoCall}, incomingCall=${widget.incomingCall}');
      return const SizedBox.shrink();
    }

    if (callState == 'connecting') {
      print('Showing connecting state');
      return Scaffold(
        backgroundColor: Colors.black.withValues(alpha: 0.9),
        body: Center(
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(16)),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(color: Colors.indigo),
                const SizedBox(height: 16),
                const Text('Connecting...', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                const Text('Setting up video call with student...', style: TextStyle(color: Colors.grey)),
                if (mediaError.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(mediaError, style: const TextStyle(color: Colors.red)),
                ],
                if (error.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(error, style: const TextStyle(color: Colors.red)),
                ],
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    print('Cancel button tapped');
                    endCall();
                  },
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                  child: const Text('Cancel', style: TextStyle(color: Colors.white)),
                ),
              ],
            ),
          ),
        ),
      );
    }

    if (callState == 'connected') {
      print('Showing connected state');
      return Scaffold(
        backgroundColor: Colors.grey.shade900,
        body: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.grey.shade800,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.circle, color: Colors.green, size: 12),
                      const SizedBox(width: 8),
                      const Text('Connected', style: TextStyle(color: Colors.white)),
                      const SizedBox(width: 8),
                      Text(
                        'Student ID: ${widget.incomingCall!['student_id'] ?? 'Unknown'}',
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                  Text(
                    'Duration: ${formatDuration(callDuration)}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    return GridView.count(
                      crossAxisCount: constraints.maxWidth > 600 ? 2 : 1,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      children: [
                        // Remote Video
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.black,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Stack(
                            children: [
                              if (remoteVideoTrack != null)
                                livekit.VideoTrackRenderer(remoteVideoTrack!),
                              Positioned(
                                bottom: 16,
                                left: 16,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  color: Colors.black.withValues(alpha: 0.5),
                                  child: Row(
                                    children: [
                                      Text(
                                        'Student: ${widget.incomingCall!['student_id'] ?? 'Unknown'}',
                                        style: const TextStyle(color: Colors.white),
                                      ),
                                      if (isRemoteAudioMuted)
                                        const Icon(Icons.volume_off, color: Colors.white, size: 16),
                                    ],
                                  ),
                                ),
                              ),
                              if (remoteParticipants.isEmpty)
                                const Center(
                                  child: Text(
                                    'Waiting for student...',
                                    style: TextStyle(color: Colors.white),
                                  ),
                                ),
                            ],
                          ),
                        ),
                        // Local Video
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.black,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Stack(
                            children: [
                              if (localVideoTrack != null)
                                livekit.VideoTrackRenderer(localVideoTrack!),
                              Positioned(
                                bottom: 16,
                                left: 16,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  color: Colors.black.withValues(alpha: 0.5),
                                  child: const Text(
                                    'You (Mentor)',
                                    style: TextStyle(color: Colors.white),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Screen Share
                        if (isScreenSharing && remoteScreenTrack != null)
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.black,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Stack(
                              children: [
                                livekit.VideoTrackRenderer(remoteScreenTrack!),
                                Positioned(
                                  bottom: 16,
                                  left: 16,
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    color: Colors.black.withOpacity(0.5),
                                    child: const Text(
                                      'Screen Share',
                                      style: TextStyle(color: Colors.white),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    );
                  },
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.grey.shade800,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    onPressed: () {
                      print('Mute button tapped');
                      toggleMute();
                    },
                    icon: Icon(
                      isMuted ? Icons.mic_off : Icons.mic,
                      color: Colors.white,
                    ),
                    color: isMuted ? Colors.red : Colors.grey,
                  ),
                  IconButton(
                    onPressed: () {
                      print('Video button tapped');
                      toggleVideo();
                    },
                    icon: Icon(
                      isVideoOff ? Icons.videocam_off : Icons.videocam,
                      color: Colors.white,
                    ),
                    color: isVideoOff ? Colors.red : Colors.grey,
                  ),
                  IconButton(
                    onPressed: () {
                      print('Screen share button tapped');
                      toggleScreenShare();
                    },
                    icon: const Icon(Icons.screen_share, color: Colors.white),
                    color: isScreenSharing ? Colors.blue : Colors.grey,
                  ),
                  IconButton(
                    onPressed: () {
                      print('End call button tapped');
                      endCall();
                    },
                    icon: const Icon(Icons.call_end, color: Colors.white),
                    color: Colors.red,
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    if (callState == 'ended' || callState == 'error') {
      print('Showing $callState state');
      return Scaffold(
        backgroundColor: Colors.black.withOpacity(0.75),
        body: Center(
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('📞', style: TextStyle(fontSize: 48)),
                Text(
                  callState == 'error' ? 'Call Failed' : 'Call Ended',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (callState == 'ended')
                  Text(
                    'Duration: ${formatDuration(callDuration)}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                if (error.isNotEmpty)
                  Text(
                    error,
                    style: const TextStyle(color: Colors.red),
                  ),
                if (mediaError.isNotEmpty)
                  Text(
                    mediaError,
                    style: const TextStyle(color: Colors.red),
                  ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    print('Close button tapped');
                    widget.onCallStatusChange?.call('ended', {});
                  },
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.indigo),
                  child: const Text(
                    'Close',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return const SizedBox.shrink();
  }
}

// Extension to safely find first element or null
extension IterableExtension<T> on Iterable<T> {
  T? firstWhereOrNull(bool Function(T) test) {
    try {
      return firstWhere(test);
    } catch (e) {
      return null;
    }
  }
}
