import 'package:flutter/material.dart';
import '../../../core/widgets/base_page.dart';
import '../../common/presentation/pages/feature_placeholder_page.dart';

class ListCenterPage extends StatelessWidget {
  const ListCenterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const FeaturePlaceholderPage(
      title: 'List Centers',
      subtitle: 'View and manage all centers',
      breadcrumbs: ['Dashboard', 'Director', 'List Centers'],
      featureName: 'List Centers',
    );
  }
}
