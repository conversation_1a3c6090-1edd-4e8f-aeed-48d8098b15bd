/// otp_controller.dart - OTP Controller for MVC Architecture
///
/// English: This controller acts as an intermediary between the OtpModel and OtpView.
/// It handles user interactions, coordinates OTP verification logic, manages navigation,
/// countdown timer display, and provides a clean interface for the view to interact
/// with the model. This follows the MVC pattern where the controller manages the flow
/// between model and view.
///
/// Tanglish: OtpModel and OtpView ku naduvula irukka vendiya controller. User interactions
/// handle pannum, OTP verification logic coordinate pannum, navigation manage pannum,
/// countdown timer display pannum, view ku model oda interact panna clean interface
/// provide pannum. MVC pattern follow pannitu controller model and view ku naduvula
/// flow manage pannum.
///
/// Key Responsibilities:
/// - Handle OTP input and field navigation
/// - Coordinate OTP verification business logic
/// - Manage countdown timer and resend functionality
/// - Handle navigation flow after verification
/// - Provide clean interface between model and view
/// - Handle animation triggers and UI state changes
library;

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../models/otp_model.dart';
import '../../../core/models/auth_models.dart';
import '../../../core/utils/logger.dart';

/// OtpController - Controller for OTP Verification Flow Management
///
/// English: Controller class that manages the OTP verification flow and coordinates between model and view.
/// Tanglish: OTP verification flow manage panna vendiya controller class, model and view ku naduvula coordinate pannum.
class OtpController extends ChangeNotifier {
  final OtpModel _model;
  late final VoidCallback _authServiceListener;
  
  // Animation and UI state
  bool _shouldTriggerShakeAnimation = false;
  
  OtpController(this._model) {
    _authServiceListener = _handleAuthStateChange;
    _model.listenToAuthService(_authServiceListener);
    _model.initialize(); // Start countdown timer
  }
  
  // Getters that delegate to model
  List<String> get otpDigits => _model.otpDigits;
  String get otpValue => _model.otpValue;
  bool get isLoading => _model.isLoading;
  String? get errorMessage => _model.errorMessage;
  int get resendCountdown => _model.resendCountdown;
  bool get canResend => _model.canResend;
  bool get isOtpComplete => _model.isOtpComplete;
  bool get isOtpValid => _model.isOtpValid;
  bool get shouldTriggerShakeAnimation => _shouldTriggerShakeAnimation;
  
  /// Handle OTP digit input change
  void onOtpDigitChanged(int index, String value, List<FocusNode> focusNodes) {
    _model.setOtpDigit(index, value);
    
    if (value.isNotEmpty) {
      // Move to next field
      if (index < 5) {
        focusNodes[index + 1].requestFocus();
      } else {
        // Last digit entered, unfocus and attempt verification
        focusNodes[index].unfocus();
        if (_model.isOtpComplete) {
          onOtpSubmit();
        }
      }
    } else if (value.isEmpty && index > 0) {
      // Move to previous field on backspace
      focusNodes[index - 1].requestFocus();
    }
  }
  
  /// Handle OTP form submission
  Future<void> onOtpSubmit() async {
    if (!_model.validateOtp()) {
      _triggerShakeAnimation();
      return;
    }
    
    // Clear any previous errors
    _model.clearError();
    
    // Add haptic feedback
    HapticFeedback.lightImpact();
    
    final result = await _model.verifyOtp();
    
    if (!result.success) {
      _triggerShakeAnimation();
      HapticFeedback.heavyImpact();
    }
  }
  
  /// Handle resend OTP request
  Future<void> onResendOtp() async {
    if (!_model.canResend) return;
    
    final success = await _model.resendOtp();
    
    if (success) {
      HapticFeedback.lightImpact();
    }
  }
  
  /// Handle error dismissal
  void onErrorDismiss() {
    _model.clearError();
  }
  
  /// Handle back navigation
  void onBackPressed(BuildContext context) {
    context.go('/auth/login');
  }
  
  /// Clear all OTP fields
  void clearOtpFields() {
    _model.clearOtp();
  }
  
  /// Trigger shake animation for errors
  void _triggerShakeAnimation() {
    _shouldTriggerShakeAnimation = true;
    notifyListeners();
    
    // Reset animation trigger after a short delay
    Future.delayed(const Duration(milliseconds: 100), () {
      _shouldTriggerShakeAnimation = false;
      notifyListeners();
    });
  }
  
  /// Handle authentication state changes
  void _handleAuthStateChange() {
    final authState = _model.authState;
    final authError = _model.authServiceError;
    
    // Update loading state based on auth service
    notifyListeners();
    
    AppLogger.auth('Auth state changed in OTP controller: ${authState.name}');
    
    // Handle specific state changes
    if (authState == AuthState.error && authError != null) {
      _triggerShakeAnimation();
      clearOtpFields();
    }
  }
  
  /// Navigate based on authentication state
  void handleNavigation(BuildContext context) {
    final authState = _model.authState;
    
    switch (authState) {
      case AuthState.authenticated:
        context.go('/dashboard');
        break;
      case AuthState.unauthenticated:
        context.go('/auth/login');
        break;
      case AuthState.error:
        // Error handling is done through model error message
        break;
      default:
        // Stay on OTP page
        break;
    }
  }
  
  /// Reset controller state
  void reset() {
    _model.reset();
    _shouldTriggerShakeAnimation = false;
    notifyListeners();
  }
  
  /// Check if should show loading state
  bool get shouldShowLoading => _model.isLoading || _model.authState == AuthState.loading;
  
  /// Get effective error message (model error or auth service error)
  String? get effectiveErrorMessage => _model.errorMessage ?? _model.authServiceError;
  
  /// Get resend countdown display text
  String get resendCountdownText {
    if (_model.canResend) {
      return 'Resend OTP';
    } else {
      return 'Resend in ${_model.resendCountdown}s';
    }
  }
  
  /// Check if OTP field should be highlighted (has value)
  bool isOtpFieldHighlighted(int index) {
    return index < _model.otpDigits.length && _model.otpDigits[index].isNotEmpty;
  }
  
  @override
  void dispose() {
    _model.removeAuthServiceListener(_authServiceListener);
    _model.dispose();
    super.dispose();
  }
}
