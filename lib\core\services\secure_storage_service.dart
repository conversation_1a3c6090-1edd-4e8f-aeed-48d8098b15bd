import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../utils/logger.dart';

class SecureStorageService {
  static const _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      // accessibility: KeychainItemAccessibility.first_unlock_this_device,
    ),
  );

  static SharedPreferences? _prefs;

  /// Initialize secure storage service
  static Future<void> init() async {
    if (kIsWeb) {
      _prefs = await SharedPreferences.getInstance();
    }
  }

  /// Write a value to secure storage
  static Future<void> write({required String key, required String value}) async {
    try {
      if (kIsWeb) {
        // On web, use SharedPreferences with a prefix for security
        await _prefs?.setString('secure_$key', value);
      } else {
        // On mobile platforms, use FlutterSecureStorage
        await _secureStorage.write(key: key, value: value);
      }
    } catch (e) {
      AppLogger.error('Failed to write to secure storage: $e');
      rethrow;
    }
  }

  /// Read a value from secure storage
  static Future<String?> read({required String key}) async {
    try {
      if (kIsWeb) {
        // On web, use SharedPreferences with a prefix
        return _prefs?.getString('secure_$key');
      } else {
        // On mobile platforms, use FlutterSecureStorage
        return await _secureStorage.read(key: key);
      }
    } catch (e) {
      AppLogger.error('Failed to read from secure storage: $e');
      return null;
    }
  }

  /// Delete a value from secure storage
  static Future<void> delete({required String key}) async {
    try {
      if (kIsWeb) {
        // On web, use SharedPreferences with a prefix
        await _prefs?.remove('secure_$key');
      } else {
        // On mobile platforms, use FlutterSecureStorage
        await _secureStorage.delete(key: key);
      }
    } catch (e) {
      AppLogger.error('Failed to delete from secure storage: $e');
    }
  }

  /// Delete all values from secure storage
  static Future<void> deleteAll() async {
    try {
      if (kIsWeb) {
        // On web, remove all keys with the secure prefix
        final keys = _prefs?.getKeys().where((key) => key.startsWith('secure_')).toList() ?? [];
        for (final key in keys) {
          await _prefs?.remove(key);
        }
      } else {
        // On mobile platforms, use FlutterSecureStorage
        await _secureStorage.deleteAll();
      }
    } catch (e) {
      AppLogger.error('Failed to delete all from secure storage: $e');
    }
  }

  /// Check if a key exists in secure storage
  static Future<bool> containsKey({required String key}) async {
    try {
      if (kIsWeb) {
        return _prefs?.containsKey('secure_$key') ?? false;
      } else {
        return await _secureStorage.containsKey(key: key);
      }
    } catch (e) {
      AppLogger.error('Failed to check key in secure storage: $e');
      return false;
    }
  }
}
