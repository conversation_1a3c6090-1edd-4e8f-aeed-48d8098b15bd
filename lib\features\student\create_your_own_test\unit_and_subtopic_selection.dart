import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import '../../../core/config/app_config.dart';
import '../../../core/widgets/background.dart';
import './test_data.dart';

final testProvider = StateProvider<TestData?>((ref) => null);
final isGeneratingTestProvider = StateProvider<bool>((ref) => false);
final unitsProvider = StateProvider<List<String>>((ref) => []);
final subtopicsProvider = StateProvider<Map<String, List<String>>>((ref) => {});
final isLoadingUnitsProvider = StateProvider<bool>((ref) => false);
final isLoadingSubtopicsProvider = StateProvider<bool>((ref) => false);
final unitErrorProvider = StateProvider<String?>((ref) => null);
final subtopicErrorProvider = StateProvider<String?>((ref) => null);

class UnitAndSubtopicSelection extends ConsumerStatefulWidget {
  final String selectedExam;
  final String selectedModule;
  final List<String> selectedUnits;
  final List<String> selectedSubtopics;
  final ValueChanged<List<String>> setSelectedUnits;
  final ValueChanged<List<String>> setSelectedSubtopics;
  final ValueChanged<bool> setTestStarted;

  const UnitAndSubtopicSelection({
    super.key,
    required this.selectedExam,
    required this.selectedModule,
    required this.selectedUnits,
    required this.selectedSubtopics,
    required this.setSelectedUnits,
    required this.setSelectedSubtopics,
    required this.setTestStarted,
  });

  @override
  ConsumerState<UnitAndSubtopicSelection> createState() =>
      _UnitAndSubtopicSelectionState();
}

class _UnitAndSubtopicSelectionState
    extends ConsumerState<UnitAndSubtopicSelection> {
  final TextEditingController _numQuestionsController =
      TextEditingController(text: '20');
  String _testDuration = '30 minutes';
  String? _activeUnit;

  @override
  void initState() {
    super.initState();
    // Delay _fetchUnits to run after widget tree is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchUnits();
    });
  }

  @override
  void dispose() {
    _numQuestionsController.dispose();
    super.dispose();
  }

  Future<void> _fetchUnits() async {
    ref.read(isLoadingUnitsProvider.notifier).state = true;
    try {
      final response = await http
          .get(
            Uri.parse(
                '${AppConfig.apiBaseUrl}${AppConfig.examUnitsEndpoint}?exam_name=${widget.selectedExam}&subject=${widget.selectedModule}'),
            headers: AppConfig.defaultHeaders,
          )
          .timeout(AppConfig.apiTimeout);

      if (kDebugMode) {
        debugPrint(
            'Units API Request: ${AppConfig.apiBaseUrl}${AppConfig.examUnitsEndpoint}?exam_name=${widget.selectedExam}&subject=${widget.selectedModule}');
        debugPrint('Units Status Code: ${response.statusCode}');
        debugPrint('Units Response Body: ${response.body}');
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        ref.read(unitsProvider.notifier).state =
            List<String>.from(data['units'] ?? []);
        ref.read(isLoadingUnitsProvider.notifier).state = false;
      } else {
        ref.read(unitErrorProvider.notifier).state =
            'Failed to load units: ${response.statusCode}';
        ref.read(isLoadingUnitsProvider.notifier).state = false;
      }
    } catch (e) {
      ref.read(unitErrorProvider.notifier).state = 'Error fetching units: $e';
      ref.read(isLoadingUnitsProvider.notifier).state = false;
    }
  }

  Future<void> _fetchSubtopics(String unit) async {
    ref.read(isLoadingSubtopicsProvider.notifier).state = true;
    try {
      final response = await http
          .get(
            Uri.parse(
                '${AppConfig.apiBaseUrl}${AppConfig.examSubtopicsEndpoint}?exam_name=${widget.selectedExam}&subject=${widget.selectedModule}&unit=$unit'),
            headers: AppConfig.defaultHeaders,
          )
          .timeout(AppConfig.apiTimeout);

      if (kDebugMode) {
        debugPrint(
            'Subtopics API Request: ${AppConfig.apiBaseUrl}${AppConfig.examSubtopicsEndpoint}?exam_name=${widget.selectedExam}&subject=${widget.selectedModule}&unit=$unit');
        debugPrint('Subtopics Status Code: ${response.statusCode}');
        debugPrint('Subtopics Response Body: ${response.body}');
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        ref.read(subtopicsProvider.notifier).update((state) => {
              ...state,
              unit: List<String>.from(data['subtopics'] ?? []),
            });
        ref.read(isLoadingSubtopicsProvider.notifier).state = false;
      } else {
        ref.read(subtopicErrorProvider.notifier).state =
            'Failed to load subtopics: ${response.statusCode}';
        ref.read(isLoadingSubtopicsProvider.notifier).state = false;
      }
    } catch (e) {
      ref.read(subtopicErrorProvider.notifier).state =
          'Error fetching subtopics: $e';
      ref.read(isLoadingSubtopicsProvider.notifier).state = false;
    }
  }

Future<void> _startTest() async {
  if (widget.selectedSubtopics.isEmpty) {
    Fluttertoast.showToast(
      msg: 'Please select at least one subtopic.',
      backgroundColor: Colors.red,
    );
    return;
  }

  final numQuestions = int.tryParse(_numQuestionsController.text);
  if (numQuestions == null || numQuestions < 1 || numQuestions > 50) {
    Fluttertoast.showToast(
      msg: 'Please enter a valid number of questions (1-50).',
      backgroundColor: Colors.red,
    );
    return;
  }

  ref.read(isGeneratingTestProvider.notifier).state = true;

  final normalizedSubject = widget.selectedModule.toLowerCase();
  String userId;
  try {
    final prefs = await SharedPreferences.getInstance();
    final userDataString = prefs.getString(AppConfig.userDataKey);
    if (userDataString != null) {
      final userData = json.decode(userDataString);
      userId = userData['id']?.toString() ?? 'user_${DateTime.now().millisecondsSinceEpoch}';
    } else {
      userId = 'user_${DateTime.now().millisecondsSinceEpoch}';
      await prefs.setString(AppConfig.userDataKey, json.encode({'id': userId}));
    }
  } catch (e) {
    Fluttertoast.showToast(
      msg: 'Error fetching user ID: $e',
      backgroundColor: Colors.red,
    );
    ref.read(isGeneratingTestProvider.notifier).state = false;
    return;
  }

  final payload = {
    'num_questions': numQuestions,
    'exam_name': widget.selectedExam,
    'unit_name': widget.selectedUnits.join(', '),
    'sub_topics': widget.selectedSubtopics.join(', '),
    'user_id': userId,
    'duration': _testDuration,
    'subject': normalizedSubject,
    'subject_name': widget.selectedModule,
  };

  if (kDebugMode) {
    debugPrint('Start Test Payload: ${jsonEncode(payload)}');
  }

  String endpoint;
  switch (normalizedSubject) {
    case 'biology':
      endpoint = '${AppConfig.apiBaseUrl}/start-test';
      break;
    case 'chemistry':
      endpoint = '${AppConfig.apiBaseUrl}/start-test-chemistry';
      break;
    case 'mathematics':
    case 'math':
      endpoint = '${AppConfig.apiBaseUrl}/start-test-math';
      break;
    case 'physics':
      endpoint = '${AppConfig.apiBaseUrl}/start-test-physics';
      break;
    default:
      Fluttertoast.showToast(
        msg: 'Invalid subject selected: $normalizedSubject',
        backgroundColor: Colors.red,
      );
      ref.read(isGeneratingTestProvider.notifier).state = false;
      return;
  }

  try {
    final response = await http.post(
      Uri.parse(endpoint),
      headers: {
        ...AppConfig.defaultHeaders,
        'Content-Type': 'application/json',
      },
      body: jsonEncode(payload),
    ).timeout(AppConfig.apiTimeout);

    if (kDebugMode) {
      debugPrint('Start Test API Request: $endpoint');
      debugPrint('Start Test Status Code: ${response.statusCode}');
      debugPrint('Start Test Response Body: ${response.body}');
    }

    if (response.statusCode == 200) {
      final result = json.decode(response.body);
      debugPrint('API Response: ${jsonEncode(result)}');
      if (result['message']?.toLowerCase().contains('test started successfully') ?? false) {
        final testData = TestData.fromJson(
          result,
          examName: widget.selectedExam, // Pass exam_name
          subject: normalizedSubject, // Pass subject
        );
        debugPrint('Parsed TestData: examId=${testData.examId}, questions=${testData.questions.length}');
        ref.read(testProvider.notifier).state = testData;
        debugPrint('testProvider after set: ${ref.read(testProvider)}');
        widget.setTestStarted(true);

        Fluttertoast.showToast(
          msg: 'Test created successfully!',
          backgroundColor: Colors.green,
        );
      } else {
        throw Exception(result['error'] ?? 'Unknown error starting test');
      }
    } else {
      throw Exception('Failed to start test: ${response.statusCode}');
    }
  } catch (e) {
    Fluttertoast.showToast(
      msg: 'Error starting test: $e',
      backgroundColor: Colors.red,
    );
    if (kDebugMode) {
      debugPrint('Start Test Error: $e');
    }
  } finally {
    ref.read(isGeneratingTestProvider.notifier).state = false;
  }
}



 Widget _renderLoader(String text) {
    return Center(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          FaIcon(
            FontAwesomeIcons.spinner,
            size: 24,
            color: const Color(0xFF2563EB),
          )
              .animate(
                onPlay: (controller) => controller.repeat(),
              )
              .rotate(
                duration: const Duration(seconds: 1),
                curve: Curves.linear,
              ),
          const SizedBox(width: 12),
          Text(
            text,
            style: TextStyle(fontSize: 18, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  Widget _renderError(String? error, String message) {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.red.shade50,
          border: Border.all(color: Colors.red.shade200),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            FaIcon(
              FontAwesomeIcons.triangleExclamation,
              color: Colors.red.shade700,
              size: 20,
            ),
            const SizedBox(width: 12),
            Text(
              error ?? message,
              style: TextStyle(color: Colors.red.shade700),
            ),
          ],
        ),
      ),
    );
  }

  Widget _renderUnitSkeleton() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: List.generate(
          5,
          (index) => Container(
            height: 48,
            margin: const EdgeInsets.only(bottom: 12),
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(8),
            ),
          ).animate().shimmer(),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final units = ref.watch(unitsProvider);
    final subtopics = ref.watch(subtopicsProvider);
    final isLoadingUnits = ref.watch(isLoadingUnitsProvider);
    final isLoadingSubtopics = ref.watch(isLoadingSubtopicsProvider);
    final unitError = ref.watch(unitErrorProvider);
    final subtopicError = ref.watch(subtopicErrorProvider);
    final isGeneratingTest = ref.watch(isGeneratingTestProvider);

    final areAllUnitsSelected =
        units.isNotEmpty && widget.selectedUnits.length == units.length;
    final subtopicsForActiveUnit =
        (_activeUnit != null ? subtopics[_activeUnit] : null) ?? <String>[];
    final areAllSubtopicsSelected = subtopicsForActiveUnit.isNotEmpty &&
        subtopicsForActiveUnit
            .every((s) => widget.selectedSubtopics.contains(s));

    return Scaffold(
      body: Stack(
        children: [
          const CosmicBackground(),
          if (isGeneratingTest)
            Container(
              color: Colors.black.withOpacity(0.8),
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const FaIcon(
                      FontAwesomeIcons.spinner,
                      size: 48,
                      color: Color(0xFF2563EB),
                    )
                        .animate(
                          onPlay: (controller) => controller.repeat(),
                        )
                        .rotate(
                          duration: const Duration(seconds: 1),
                          curve: Curves.linear,
                        ),
                    const SizedBox(height: 16),
                    const Text(
                      'Generating Your Questions...',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Crafting a unique test from ${widget.selectedSubtopics.length} topics with ${_numQuestionsController.text} questions.',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          SafeArea(
            child: Center(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 1280),
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Test Builder',
                          style: TextStyle(
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2563EB),
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Craft your personalized practice test by following the steps below.',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 24),
                        LayoutBuilder(
                          builder: (context, constraints) {
                            final isDesktop = constraints.maxWidth >= 768;
                            return isDesktop
                                ? Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                          child: _buildUnitsColumn(
                                              units,
                                              isLoadingUnits,
                                              unitError,
                                              areAllUnitsSelected)),
                                      const SizedBox(width: 24),
                                      Expanded(
                                          child: _buildSubtopicsColumn(
                                              subtopicsForActiveUnit,
                                              isLoadingSubtopics,
                                              subtopicError,
                                              areAllSubtopicsSelected)),
                                      const SizedBox(width: 24),
                                      Expanded(child: _buildConfigColumn()),
                                    ],
                                  )
                                : Column(
                                    children: [
                                      _buildUnitsColumn(units, isLoadingUnits,
                                          unitError, areAllUnitsSelected),
                                      const SizedBox(height: 24),
                                      _buildSubtopicsColumn(
                                          subtopicsForActiveUnit,
                                          isLoadingSubtopics,
                                          subtopicError,
                                          areAllSubtopicsSelected),
                                      const SizedBox(height: 24),
                                      _buildConfigColumn(),
                                    ],
                                  );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnitsColumn(List<String> units, bool isLoadingUnits,
      String? unitError, bool areAllUnitsSelected) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                FaIcon(
                  FontAwesomeIcons.layerGroup,
                  size: 24,
                  color: const Color(0xFF2563EB),
                ).animate().scale(delay: const Duration(milliseconds: 100)),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Step 1: Select Units',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const Text(
                        'Click a unit to see its subtopics.',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
                if (!isLoadingUnits && units.isNotEmpty)
                  Row(
                    children: [
                      Checkbox(
                        value: areAllUnitsSelected,
                        onChanged: (value) {
                          widget.setSelectedUnits(value! ? units : []);
                          if (!value) {
                            widget.setSelectedSubtopics([]);
                            setState(() => _activeUnit = null);
                          }
                        },
                        activeColor: const Color(0xFF2563EB),
                      ),
                      const Text('All', style: TextStyle(fontSize: 14)),
                    ],
                  ),
              ],
            ),
            const SizedBox(height: 16),
            if (isLoadingUnits) _renderUnitSkeleton(),
            if (unitError != null)
              _renderError(unitError, 'Could not load units'),
            if (!isLoadingUnits && unitError == null)
              SizedBox(
                height: 300, // Adjust this height based on your design needs
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const AlwaysScrollableScrollPhysics(),
                  itemCount: units.length,
                  itemBuilder: (context, index) {
                    final unit = units[index];
                    final isSelected = widget.selectedUnits.contains(unit);
                    return GestureDetector(
                      onTap: () {
                        setState(() => _activeUnit = unit);
                        if (!isSelected) {
                          widget.setSelectedUnits(
                              [...widget.selectedUnits, unit]);
                          _fetchSubtopics(unit);
                        }
                      },
                      child: Container(
                        margin: const EdgeInsets.only(bottom: 8),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: _activeUnit == unit
                              ? const Color(0xFF2563EB)
                              : isSelected
                                  ? Colors.blue.shade100
                                  : Colors.white,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            FaIcon(
                              FontAwesomeIcons.bookmark,
                              color: _activeUnit == unit
                                  ? Colors.white70
                                  : const Color(0xFF2563EB).withOpacity(0.7),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                unit,
                                style: TextStyle(
                                  color: _activeUnit == unit
                                      ? Colors.white
                                      : Colors.black87,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            FaIcon(
                              FontAwesomeIcons.chevronRight,
                              size: 16,
                              color: _activeUnit == unit
                                  ? Colors.white
                                  : Colors.grey,
                            ),
                          ],
                        ),
                      ).animate(
                        delay: Duration(milliseconds: 50 * index),
                        effects: const [
                          FadeEffect(duration: Duration(milliseconds: 300)),
                          MoveEffect(
                            begin: Offset(-20, 0),
                            end: Offset.zero,
                            duration: Duration(milliseconds: 500),
                            curve: Curves.easeOut,
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubtopicsColumn(
      List<String> subtopicsForActiveUnit,
      bool isLoadingSubtopics,
      String? subtopicError,
      bool areAllSubtopicsSelected) {
    return _activeUnit != null
        ? Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      FaIcon(
                        FontAwesomeIcons.sitemap,
                        size: 24,
                        color: const Color(0xFF2563EB),
                      )
                          .animate()
                          .scale(delay: const Duration(milliseconds: 100)),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Step 2: Choose Subtopics',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            Text(
                              'For unit: $_activeUnit',
                              style: const TextStyle(
                                fontSize: 14,
                                color: Color(0xFF2563EB),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (!isLoadingSubtopics &&
                          subtopicsForActiveUnit.isNotEmpty)
                        Row(
                          children: [
                            Checkbox(
                              value: areAllSubtopicsSelected,
                              onChanged: (value) {
                                final currentSubtopics = widget
                                    .selectedSubtopics
                                    .where((s) =>
                                        !subtopicsForActiveUnit.contains(s))
                                    .toList();
                                widget.setSelectedSubtopics(
                                  value!
                                      ? [
                                          ...currentSubtopics,
                                          ...subtopicsForActiveUnit
                                        ]
                                      : currentSubtopics,
                                );
                              },
                              activeColor: const Color(0xFF2563EB),
                            ),
                            const Text('All', style: TextStyle(fontSize: 14)),
                          ],
                        ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (isLoadingSubtopics) _renderLoader('Loading subtopics...'),
                  if (subtopicError != null)
                    _renderError(subtopicError, 'Could not load subtopics'),
                  if (!isLoadingSubtopics && subtopicError == null)
                    ListView.builder(
                      shrinkWrap: true,
                      itemCount: subtopicsForActiveUnit.length,
                      itemBuilder: (context, index) {
                        final subtopic = subtopicsForActiveUnit[index];
                        final isSelected =
                            widget.selectedSubtopics.contains(subtopic);
                        return GestureDetector(
                          onTap: () => widget.setSelectedSubtopics(
                            isSelected
                                ? widget.selectedSubtopics
                                    .where((s) => s != subtopic)
                                    .toList()
                                : [...widget.selectedSubtopics, subtopic],
                          ),
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            margin: const EdgeInsets.only(bottom: 8),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? Colors.blue.shade100
                                  : Colors.white,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Checkbox(
                                  value: isSelected,
                                  onChanged: (value) =>
                                      widget.setSelectedSubtopics(
                                    value!
                                        ? [
                                            ...widget.selectedSubtopics,
                                            subtopic
                                          ]
                                        : widget.selectedSubtopics
                                            .where((s) => s != subtopic)
                                            .toList(),
                                  ),
                                  activeColor: const Color(0xFF2563EB),
                                ),
                                FaIcon(
                                  FontAwesomeIcons.clipboardList,
                                  color: Colors.grey.shade400,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    subtopic,
                                    style: const TextStyle(
                                      color: Colors.black87,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ).animate(
                            delay: Duration(milliseconds: 50 * index),
                            effects: const [
                              FadeEffect(duration: Duration(milliseconds: 300)),
                              MoveEffect(
                                begin: Offset(-20, 0),
                                end: Offset.zero,
                                duration: Duration(milliseconds: 500),
                                curve: Curves.easeOut,
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                ],
              ),
            ),
          )
        : Center(
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    FaIcon(
                      FontAwesomeIcons.bookOpen,
                      size: 48,
                      color: Colors.grey.shade400,
                    )
                        .animate()
                        .fadeIn(delay: const Duration(milliseconds: 200)),
                    const SizedBox(height: 16),
                    const Text(
                      'Dive Deeper into Your Subject',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Select a unit on the left to explore its detailed subtopics and add them to your custom test.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          );
  }

  Widget _buildConfigColumn() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                FaIcon(
                  FontAwesomeIcons.sliders,
                  size: 24,
                  color: const Color(0xFF2563EB),
                ).animate().scale(delay: const Duration(milliseconds: 200)),
                const SizedBox(width: 12),
                const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Step 3: Configure Test',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    Text(
                      'Finalize your test parameters.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Selected Units (${widget.selectedUnits.length})',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF2563EB),
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                border: Border.all(color: Colors.grey.shade200),
                borderRadius: BorderRadius.circular(8),
              ),
              constraints: const BoxConstraints(maxHeight: 80),
              child: SingleChildScrollView(
                child: widget.selectedUnits.isNotEmpty
                    ? Column(
                        children: widget.selectedUnits
                            .map((unit) => Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 4),
                                  child: Text(
                                    unit,
                                    style:
                                        const TextStyle(color: Colors.black87),
                                  ),
                                ))
                            .toList(),
                      )
                    : const Text(
                        'None selected',
                        style: TextStyle(color: Colors.grey),
                      ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Selected Subtopics (${widget.selectedSubtopics.length})',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF2563EB),
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                border: Border.all(color: Colors.grey.shade200),
                borderRadius: BorderRadius.circular(8),
              ),
              constraints: const BoxConstraints(maxHeight: 112),
              child: SingleChildScrollView(
                child: widget.selectedSubtopics.isNotEmpty
                    ? Column(
                        children: widget.selectedSubtopics
                            .map((subtopic) => Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 4),
                                  child: Text(
                                    subtopic,
                                    style:
                                        const TextStyle(color: Colors.black87),
                                  ),
                                ))
                            .toList(),
                      )
                    : const Text(
                        'None selected',
                        style: TextStyle(color: Colors.grey),
                      ),
              ),
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            Row(
              children: [
                const FaIcon(
                  FontAwesomeIcons.circleQuestion,
                  color: Color(0xFF2563EB),
                ),
                const SizedBox(width: 8),
                const Text(
                  'Number of Questions',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF2563EB),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _numQuestionsController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Please enter a number between 1 and 50.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const FaIcon(
                  FontAwesomeIcons.clock,
                  color: Color(0xFF2563EB),
                ),
                const SizedBox(width: 8),
                const Text(
                  'Test Duration',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF2563EB),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _testDuration,
              onChanged: (value) => setState(() => _testDuration = value!),
              items: const [
                DropdownMenuItem(
                    value: '30 minutes', child: Text('30 minutes')),
                DropdownMenuItem(
                    value: '45 minutes', child: Text('45 minutes')),
                DropdownMenuItem(value: '1 hour', child: Text('1 hour')),
                DropdownMenuItem(value: '1.5 hours', child: Text('1.5 hours')),
                DropdownMenuItem(value: '2 hours', child: Text('2 hours')),
              ],
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                border: Border.all(color: Colors.blue.shade200),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Row(
                children: [
                  FaIcon(
                    FontAwesomeIcons.lightbulb,
                    color: Colors.blue,
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Pro Tip',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                        Text(
                          'Use the \'All\' checkbox in the headers to quickly select all units or subtopics.',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed:
                  widget.selectedSubtopics.isNotEmpty ? _startTest : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2563EB),
                padding:
                    const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const FaIcon(
                    FontAwesomeIcons.checkCircle,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'Create Test',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class Card extends StatelessWidget {
  final Widget child;
  final EdgeInsets padding;

  const Card(
      {super.key,
      required this.child,
      this.padding = const EdgeInsets.all(16)});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      elevation: 2,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade200),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: padding,
          child: child,
        ),
      ),
    ).animate(
      effects: const [
        FadeEffect(duration: Duration(milliseconds: 500)),
        MoveEffect(
          begin: Offset(0, 20),
          end: Offset.zero,
          duration: Duration(milliseconds: 500),
          curve: Curves.easeOut,
        ),
      ],
    );
  }
}
