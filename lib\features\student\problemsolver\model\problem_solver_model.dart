import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../../core/config/app_config.dart';  // Replace with your app name

class ApiService {
  static const String baseUrl = AppConfig.baseUrl;
  static const String endpoint = AppConfig.problemSolverEndpoint;

  Future<Map<String, dynamic>> solveDoubt({
    required String text,
    required String mode,
    required String language,
    required String userId,
    bool reset = false,
    bool includeHistory = false,
  }) async {
    var request = http.MultipartRequest('POST', Uri.parse('$baseUrl$endpoint'));
    request.fields['text'] = text;
    request.fields['mode'] = mode;
    request.fields['language'] = language;
    request.fields['user_id'] = userId;
    request.fields['reset'] = reset.toString();
    request.fields['include_history'] = includeHistory.toString();

    var response = await request.send();
    if (response.statusCode == 200) {
      return jsonDecode(await response.stream.bytesToString());
    } else {
      throw Exception('Failed to solve doubt: ${response.reasonPhrase}');
    }
  }
}

class DoubtResponse {
  final String response;
  final String mode;
  final String language;
  final String timestamp;
  final List<dynamic>? history; // Optional history if includeHistory is true

  DoubtResponse({
    required this.response,
    required this.mode,
    required this.language,
    required this.timestamp,
    this.history,
  });

  factory DoubtResponse.fromJson(Map<String, dynamic> json) {
   final responseData = json['response'];
    final String responseText = responseData is Map ? responseData['response'] : (responseData?.toString() ?? '');

    return DoubtResponse(
      response: responseText,
      mode: json['mode'] ?? 'unknown',
      language: json['language'] ?? 'unknown',
      timestamp: json['timestamp'] ?? DateTime.now().toIso8601String(),
      history: json['history'],
    );
  }
  
}