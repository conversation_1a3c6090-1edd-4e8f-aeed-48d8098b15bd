import 'package:flutter/material.dart';
import '../../../core/widgets/base_page.dart';
import '../../common/presentation/pages/feature_placeholder_page.dart';

class DirectorDashboardPage extends StatelessWidget {
  const DirectorDashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const FeaturePlaceholderPage(
      title: 'Director Dashboard',
      subtitle: 'Analytics and insights dashboard',
      breadcrumbs: ['Dashboard', 'Director', 'Dashboard'],
      featureName: 'Director Dashboard',
    );
  }
}
