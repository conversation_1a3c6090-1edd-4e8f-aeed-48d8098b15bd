import 'package:flutter/material.dart';

class StatusOption {
  final String value;
  final String label;
  final Color color;
  final Color pulseColor;

  StatusOption({
    required this.value,
    required this.label,
    required this.color,
    required this.pulseColor,
  });

  static List<StatusOption> get options => [
        StatusOption(
          value: 'online',
          label: 'Online',
          color: Colors.green.shade400,
          pulseColor: Colors.green.withOpacity(0.7),
        ),
        StatusOption(
          value: 'set_away',
          label: 'Away',
          color: Colors.yellow.shade400,
          pulseColor: Colors.yellow.withOpacity(0.7),
        ),
        StatusOption(
          value: 'offline',
          label: 'Offline',
          color: Colors.red.shade400,
          pulseColor: Colors.red.withOpacity(0.7),
        ),
      ];
}