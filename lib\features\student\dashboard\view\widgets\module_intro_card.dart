// lib/features/student/dashboard/module_intro_view.dart
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../../../core/config/app_config.dart';

class ModuleIntroView extends StatefulWidget {
  const ModuleIntroView({Key? key}) : super(key: key);

  @override
  _ModuleIntroViewState createState() => _ModuleIntroViewState();
}

class _ModuleIntroViewState extends State<ModuleIntroView> {
  int? _hoveredCard;
  String _activeCategory = 'All';

  final List<Map<String, dynamic>> modules = [
    {
      'name': 'Live Streaming',
      'icon': FontAwesomeIcons.video,
      'desc': 'Interactive sessions with experts.',
      'color': Color(AppConfig.roleColors['student']!),
      'link': '/student/live_streaming',
      'details':
          'Join real-time classes with live streaming. Recordings available after sessions.',
      'category': 'Interactive',
    },
    {
      'name': 'Create Test',
      'icon': FontAwesomeIcons.penToSquare,
      'desc': 'Build your own assessments.',
      'color': Color(AppConfig.roleColors['center_counselor']!),
      'link': '/student/create-your-own-test',
      'details':
          'Customize tests with your own questions or from our question bank. Set time limits and difficulty levels.',
      'category': 'Assessment',
    },
    {
      'name': 'Mock Tests',
      'icon': FontAwesomeIcons.vials,
      'desc': 'Practice with exam simulators.',
      'color': Color(AppConfig.roleColors['student']!),
      'link': '/student/mock_test_simulation',
      'details':
          'Full-length simulated exams with detailed analytics and performance tracking.',
      'category': 'Assessment',
    },
    {
      'name': 'Video Library',
      'icon': FontAwesomeIcons.circlePlay,
      'desc': 'On-demand recorded content.',
      'color': Color(AppConfig.roleColors['center_counselor']!),
      'link': '/student/recorded-videos',
      'details': 'Recorded video available for the live streaming classes.',
      'category': 'Resources',
    },
    {
      'name': 'Booster Modules',
      'icon': FontAwesomeIcons.bolt,
      'desc': 'Quick revision boosters.',
      'color': Color(AppConfig.roleColors['student']!),
      'link': '/student/booster-module',
      'details':
          'Condensed revision packages with key concepts, formulas, and common mistakes.',
      'category': 'Resources',
    },
    {
      'name': 'E-Books',
      'icon': FontAwesomeIcons.bookOpen,
      'desc': 'Access digital books & resources.',
      'color': Color(AppConfig.roleColors['center_counselor']!),
      'link': '/student/ebook-centre',
      'details':
          'Comprehensive digital textbooks with interactive elements for each subject',
      'category': 'Resources',
    },
    {
      'name': 'Community',
      'icon': FontAwesomeIcons.users,
      'desc': 'Learn together with peers.',
      'color': Color(AppConfig.roleColors['student']!),
      'link': '/student/student_community',
      'details':
          'Discussion forums, study groups, and peer-to-peer learning with moderation.',
      'category': 'Community',
    },
    {
      'name': 'AI Tutor',
      'icon': FontAwesomeIcons.robot,
      'desc': 'Get smart guidance instantly.',
      'color': Color(AppConfig.roleColors['center_counselor']!),
      'link': '/student/ai_tutor',
      'details':
          '24/7 AI assistant that adapts to your learning style and provides personalized help.',
      'category': 'AI Tools',
    },
    {
      'name': 'Virtual Labs',
      'icon': FontAwesomeIcons.microscope,
      'desc': 'Run experiments virtually.',
      'color': Color(AppConfig.roleColors['student']!),
      'link': '/student/virtual-labs',
      'details':
          'Interactive lab simulations with real time interaction based on concept',
      'category': 'Interactive',
    },
    {
      'name': 'Problem Solver',
      'icon': FontAwesomeIcons.circleQuestion,
      'desc': 'Step-by-step solutions.',
      'color': Color(AppConfig.roleColors['center_counselor']!),
      'link': '/student/problem-solver',
      'details':
          'Detailed explanations for complex problems with multiple solution approaches and you can connect with the mentor help for any problem',
      'category': 'AI Tools',
    },
    {
      'name': 'Dashboard',
      'icon': FontAwesomeIcons.chartPie,
      'desc': 'Track your performance.',
      'color': Color(AppConfig.roleColors['student']!),
      'link': '/student/dashboard',
      'details':
          'Comprehensive analytics with progress tracking, strengths/weaknesses analysis.',
      'category': 'Resources',
    },
    {
      'name': 'Smart Suggestions',
      'icon': FontAwesomeIcons.star,
      'desc': 'Recommendations tailored to you.',
      'color': Color(AppConfig.roleColors['center_counselor']!),
      'link': '/student/recommendation',
      'details':
          'AI-powered content recommendations based on your learning patterns and goals.',
      'category': 'AI Tools',
    },
    {
      'name': 'Question Generator',
      'icon': FontAwesomeIcons.fileLines,
      'desc': 'Auto-generate practice sets.',
      'color': Color(AppConfig.roleColors['student']!),
      'link': '/student/question-generator',
      'details':
          'Upload the subject file it will be generated the question based on the content',
      'category': 'Assessment',
    },
    {
      'name': 'Sasthra Tools',
      'icon': FontAwesomeIcons.chalkboardTeacher,
      'desc': 'Advanced scientific calculators.',
      'color': Color(AppConfig.roleColors['center_counselor']!),
      'link': '/student/sasthra-tools',
      'details':
          'A specialized chatbot designed to answer your questions for each subject, with separate interactive chatbots',
      'category': 'Resources',
    },
  ];

  final categories = [
    'All',
    'Interactive',
    'Assessment',
    'Resources',
    'Community',
    'AI Tools'
  ];

  @override
  Widget build(BuildContext context) {
    final filteredModules = _activeCategory == 'All'
        ? modules
        : modules.where((mod) => mod['category'] == _activeCategory).toList();

    return Card(
      elevation: AppConfig.cardElevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
      ),
      child: Stack(
        children: [
          ...List.generate(6, (i) {
            return Positioned(
              left: (i * 90).toDouble(),
              top: (i * 80).toDouble(),
              child: Container(
                width: 80 + (i * 20),
                height: 80 + (i * 20),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: i % 2 == 0
                      ? Color(AppConfig.roleColors['student']!)
                          .withOpacity(0.07)
                      : Color(AppConfig.roleColors['center_counselor']!)
                          .withOpacity(0.07),
                ),
              ).animate().move(
                    duration: (8000 + i * 2000).ms,
                    begin: const Offset(0, 20),
                    end: const Offset(0, -20),
                    curve: Curves.easeInOut,
                  ),
            );
          }),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  alignment: WrapAlignment.center,
                  children: categories.map((category) {
                    return ActionChip(
                      label: Text(category),
                      backgroundColor: _activeCategory == category
                          ? Color(AppConfig.roleColors['student']!)
                          : Colors.white,
                      labelStyle: TextStyle(
                        color: _activeCategory == category
                            ? Colors.white
                            : Color(AppConfig.roleColors['center_counselor']!),
                      ),
                      onPressed: () =>
                          setState(() => _activeCategory = category),
                    ).animate().scale(duration: 300.ms);
                  }).toList(),
                ),
                const SizedBox(height: 16),
                GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    childAspectRatio: 0.8,
                  ),
                  itemCount: filteredModules.length,
                  itemBuilder: (context, index) {
                    final mod = filteredModules[index];
                    final isHovered = _hoveredCard == index;

                    return MouseRegion(
                      onEnter: (_) => setState(() => _hoveredCard = index),
                      onExit: (_) => setState(() => _hoveredCard = null),
                      child: GestureDetector(
                        onTap: () => context.go(mod['link']),
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 600),
                          transform: isHovered
                              ? (Matrix4.identity()..rotateY(3.14))
                              : Matrix4.identity(),
                          child: Card(
                            elevation: isHovered ? 8 : 4,
                            shape: RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.circular(AppConfig.borderRadius),
                              side: BorderSide(color: mod['color']),
                            ),
                            child: isHovered
                                ? _buildBackCard(mod)
                                : _buildFrontCard(mod, index),
                          ),
                        ),
                      ),
                    )
                        .animate()
                        .fadeIn(duration: 500.ms, delay: (index * 100).ms);
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFrontCard(Map<String, dynamic> mod, int index) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CircleAvatar(
          radius: 36,
          backgroundColor: mod['color'],
          child: FaIcon(
            mod['icon'],
            color: Colors.white,
            size: 32,
          ),
        ).animate().moveY(
              begin: -8,
              end: 0,
              duration: 1200.ms,
              curve: Curves.easeInOut,
            ),
        const SizedBox(height: 8),
        Text(
          mod['name'],
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: mod['color'],
          ),
        ),
        Text(
          mod['desc'],
          style: const TextStyle(fontSize: 14, color: Colors.black54),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Chip(
          label: const Text('Hover for details'),
          backgroundColor: mod['color'],
          labelStyle: const TextStyle(color: Colors.white),
        ).animate().fade(duration: 2000.ms, begin: 0.7, end: 1),
      ],
    );
  }

  Widget _buildBackCard(Map<String, dynamic> mod) {
    return Container(
      color: mod['color'],
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            mod['name'],
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            mod['details'],
            style: const TextStyle(fontSize: 14, color: Colors.white70),
          ),
          const Spacer(),
          ElevatedButton(
            onPressed: () => context.go(mod['link']),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: mod['color'],
            ),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('Explore Now'),
                SizedBox(width: 8),
                FaIcon(FontAwesomeIcons.arrowRight, size: 16),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
