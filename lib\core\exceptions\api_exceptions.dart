/// Base API exception class
abstract class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final dynamic originalError;

  const ApiException(this.message, {this.statusCode, this.originalError});

  @override
  String toString() => 'ApiException: $message';
}

/// Generic API exception
class GenericApiException extends ApiException {
  const GenericApiException(super.message, {super.statusCode, super.originalError});
}

/// Network connection exception
class NetworkException extends ApiException {
  const NetworkException(super.message);

  @override
  String toString() => 'NetworkException: $message';
}

/// Request timeout exception
class TimeoutException extends ApiException {
  const TimeoutException(super.message);

  @override
  String toString() => 'TimeoutException: $message';
}

/// Bad request exception (400)
class BadRequestException extends ApiException {
  const BadRequestException(super.message) : super(statusCode: 400);

  @override
  String toString() => 'BadRequestException: $message';
}

/// Unauthorized exception (401)
class UnauthorizedException extends ApiException {
  const UnauthorizedException(super.message) : super(statusCode: 401);

  @override
  String toString() => 'UnauthorizedException: $message';
}

/// Forbidden exception (403)
class ForbiddenException extends ApiException {
  const ForbiddenException(super.message) : super(statusCode: 403);

  @override
  String toString() => 'ForbiddenException: $message';
}

/// Not found exception (404)
class NotFoundException extends ApiException {
  const NotFoundException(super.message) : super(statusCode: 404);

  @override
  String toString() => 'NotFoundException: $message';
}

/// Server error exception (500)
class ServerException extends ApiException {
  const ServerException(super.message) : super(statusCode: 500);

  @override
  String toString() => 'ServerException: $message';
}

/// Authentication exceptions
class AuthenticationException extends ApiException {
  const AuthenticationException(super.message);

  @override
  String toString() => 'AuthenticationException: $message';
}

/// Token expired exception
class TokenExpiredException extends AuthenticationException {
  const TokenExpiredException() : super('Authentication token has expired');

  @override
  String toString() => 'TokenExpiredException: Authentication token has expired';
}

/// Invalid token exception
class InvalidTokenException extends AuthenticationException {
  const InvalidTokenException() : super('Authentication token is invalid');

  @override
  String toString() => 'InvalidTokenException: Authentication token is invalid';
}

/// Session expired exception
class SessionExpiredException extends AuthenticationException {
  const SessionExpiredException() : super('User session has expired');

  @override
  String toString() => 'SessionExpiredException: User session has expired';
}

/// OTP related exceptions
class OtpException extends ApiException {
  const OtpException(super.message);

  @override
  String toString() => 'OtpException: $message';
}

class InvalidOtpException extends OtpException {
  const InvalidOtpException() : super('Invalid OTP provided');

  @override
  String toString() => 'InvalidOtpException: Invalid OTP provided';
}

class OtpExpiredException extends OtpException {
  const OtpExpiredException() : super('OTP has expired');

  @override
  String toString() => 'OtpExpiredException: OTP has expired';
}

/// Cache related exceptions
class CacheException extends ApiException {
  const CacheException(super.message);

  @override
  String toString() => 'CacheException: $message';
}

/// Storage related exceptions
class StorageException extends ApiException {
  const StorageException(super.message);

  @override
  String toString() => 'StorageException: $message';
}

/// Utility class for exception handling
class ExceptionHandler {
  /// Get user-friendly message from exception
  static String getUserMessage(Exception exception) {
    if (exception is ApiException) {
      return exception.message;
    }
    
    switch (exception.runtimeType) {
      case NetworkException:
        return 'Please check your internet connection and try again.';
      case TimeoutException:
        return 'Request timed out. Please try again.';
      case UnauthorizedException:
        return 'You are not authorized to perform this action.';
      case ForbiddenException:
        return 'Access denied. Please contact support.';
      case NotFoundException:
        return 'The requested resource was not found.';
      case ServerException:
        return 'Server error occurred. Please try again later.';
      case TokenExpiredException:
      case SessionExpiredException:
        return 'Your session has expired. Please login again.';
      case InvalidOtpException:
        return 'Invalid OTP. Please check and try again.';
      case OtpExpiredException:
        return 'OTP has expired. Please request a new one.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  /// Check if exception requires logout
  static bool requiresLogout(Exception exception) {
    return exception is TokenExpiredException ||
           exception is SessionExpiredException ||
           exception is InvalidTokenException;
  }

  /// Check if exception is network related
  static bool isNetworkError(Exception exception) {
    return exception is NetworkException ||
           exception is TimeoutException;
  }

  /// Check if exception is authentication related
  static bool isAuthError(Exception exception) {
    return exception is AuthenticationException ||
           exception is UnauthorizedException ||
           exception is ForbiddenException;
  }
}
