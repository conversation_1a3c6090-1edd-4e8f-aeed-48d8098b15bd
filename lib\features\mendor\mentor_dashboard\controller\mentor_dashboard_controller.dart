import 'dart:async';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../model/mentor_model.dart';

class MentorController {
  final MentorModel model;
  final ValueNotifier<bool> isLoading = ValueNotifier(false);
  final ValueNotifier<bool> isError = ValueNotifier(false);
  final ValueNotifier<String?> errorMessage = ValueNotifier(null);
  final ValueNotifier<Map<String, dynamic>?> mentorData = ValueNotifier(null);
  final ValueNotifier<bool> isSessionLoading = ValueNotifier(false);
  final ValueNotifier<bool> isStatusOpen = ValueNotifier(false);
  final ValueNotifier<String> currentStatus = ValueNotifier('online');
  final ValueNotifier<Map<String, dynamic>?> incomingCall = ValueNotifier(null);
  final ValueNotifier<bool> isCallAccepting = ValueNotifier(false);
  final ValueNotifier<bool> showIncomingCall = ValueNotifier(false);
  final ValueNotifier<bool> isRingtoneLoaded = ValueNotifier(false);
  Timer? _pollingTimer;
  Timer? _callTimeoutTimer;
  String? mentorId;
  String? activeSessionId;
  String? token;
  final AudioPlayer _ringtonePlayer = AudioPlayer();
  bool _isRingtonePlaying = false;
  static const int callTimeoutSeconds = 30;

  MentorController({required this.model}) {
    _initRingtone();
  }

  Future<void> _initRingtone() async {
    print('Initializing ringtone player...');
    try {
      await _ringtonePlayer.setSource(AssetSource('audio/ringtone.mp3'));
      await _ringtonePlayer.setVolume(1.0);
      await _ringtonePlayer.setReleaseMode(ReleaseMode.loop);
      isRingtoneLoaded.value = true;
      print('Ringtone player initialized successfully');
    } catch (e) {
      print('Error initializing ringtone player: $e');
      isRingtoneLoaded.value = false;
    }
  }

  Future<void> _playRingtone() async {
    if (!isRingtoneLoaded.value || _isRingtonePlaying) {
      print('Ringtone not loaded or already playing');
      return;
    }

    try {
      print('Playing ringtone...');
      await _ringtonePlayer.play(AssetSource('audio/ringtone.mp3'));
      _isRingtonePlaying = true;
    } catch (e) {
      print('Error playing ringtone: $e');
      Fluttertoast.showToast(msg: 'Failed to play ringtone');
    }
  }

  Future<void> _stopRingtone() async {
    if (!_isRingtonePlaying) {
      return;
    }

    try {
      print('Stopping ringtone...');
      await _ringtonePlayer.stop();
      _isRingtonePlaying = false;
    } catch (e) {
      print('Error stopping ringtone: $e');
    }
  }

  void _startCallTimeout() {
    _callTimeoutTimer?.cancel(); // Cancel any existing timeout
    _callTimeoutTimer = Timer(const Duration(seconds: callTimeoutSeconds), () {
      print('Call timeout reached, auto-rejecting call');
      if (showIncomingCall.value) {
        handleRejectCall(); // Auto-reject the call
      }
    });
  }

  void _cancelCallTimeout() {
    _callTimeoutTimer?.cancel();
    _callTimeoutTimer = null;
  }

  Future<void> init() async {
    print('Initializing MentorController...');
    final prefs = await SharedPreferences.getInstance();
    mentorId = prefs.getString('user_id');
    activeSessionId = prefs.getString('activeSessionId');
    token = prefs.getString('token');
    print('Retrieved from SharedPreferences: mentorId=$mentorId, activeSessionId=$activeSessionId, token=$token');

    if (mentorId == null || token == null) {
      print('Warning: Missing mentorId or token in SharedPreferences');
      isError.value = true;
      errorMessage.value = 'Mentor ID or token not found. Please log in again.';
      return;
    }

    await _fetchMentorData();

    // Set default status to online on login and start polling
    await _setDefaultOnlineStatus();
  }

  Future<void> _setDefaultOnlineStatus() async {
    print('Setting default online status...');
    try {
      await model.mentorSession('online', token!);
      currentStatus.value = 'online';
      print('Default online status set successfully');
      _startCallPolling();
      Fluttertoast.showToast(msg: 'You are now online and ready to receive calls');
    } catch (e) {
      print('Failed to set default online status: $e');
      // Don't show error to user for this, just log it
    }
  }

  Future<void> _fetchMentorData() async {
    print('Fetching mentor data with token: $token');
    isLoading.value = true;
    try {
      mentorData.value = await model.getDirectorMentorDashboard(token!);
      isError.value = false;
      errorMessage.value = null;
    } catch (e) {
      print('Error fetching mentor data: $e');
      isError.value = true;
      errorMessage.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }

  void _startCallPolling() {
    print('Starting call polling for mentorId: $mentorId');
    if (_pollingTimer != null || mentorId == null || token == null) {
      print('Polling already active or missing mentorId/token');
      return;
    }

    _pollingTimer = Timer.periodic(const Duration(seconds: 5), (timer) async {
      try {
        final data = await model.pollForCalls(mentorId!, token!);
        print('Poll data received: $data');
        if (data['incoming_call'] != null && data['room_name'] != null && data['student_id'] != null) {
          if (incomingCall.value == null && !showIncomingCall.value) {
            print('New incoming call detected: $data');
            incomingCall.value = {
              'room_name': data['room_name'],
              'student_id': data['student_id'],
              'timestamp': data['timestamp'],
              'token': data['token'] ?? 'mock_token', // Replace with actual token if provided
            };
            showIncomingCall.value = true;
            await _playRingtone();
            _startCallTimeout();
          }
        }
      } catch (e) {
        print('Polling error: $e');
      }
    });
  }

  void stopCallPolling() {
    print('Stopping call polling');
    _pollingTimer?.cancel();
    _pollingTimer = null;
    _cancelCallTimeout();
    _stopRingtone();
  }

  Future<void> handleAcceptCall() async {
    print('Handling accept call...');
    if (incomingCall.value == null || isCallAccepting.value || mentorId == null || token == null) {
      print('Cannot accept call: missing data or already accepting');
      return;
    }

    isCallAccepting.value = true;
    try {
      _cancelCallTimeout();
      await _stopRingtone();
      await model.acceptCall(mentorId!, incomingCall.value!['room_name'], token!);
      print('Call accepted successfully');
      showIncomingCall.value = false;
      Fluttertoast.showToast(msg: 'Call accepted! Connecting to video call...');
    } catch (e) {
      print('Error accepting call: $e');
      Fluttertoast.showToast(msg: 'Failed to accept call: $e');
    } finally {
      isCallAccepting.value = false;
    }
  }

  Future<void> handleRejectCall() async {
    print('Handling reject call...');
    if (incomingCall.value == null || token == null) {
      print('No incoming call or token to reject');
      return;
    }

    try {
      _cancelCallTimeout();
      await _stopRingtone();
      await model.endCall(incomingCall.value!['room_name'], token!);
      print('Call rejected successfully');
      Fluttertoast.showToast(msg: 'Call declined');
    } catch (e) {
      print('Error rejecting call: $e');
    }
    incomingCall.value = null;
    showIncomingCall.value = false;
  }

  Future<void> handleStatusChange(String status) async {
    print('Changing status to: $status');
    if (token == null) {
      print('No token found for status change');
      Fluttertoast.showToast(msg: 'Authentication token missing.');
      isStatusOpen.value = false;
      return;
    }

    isSessionLoading.value = true;
    try {
      final response = await model.mentorSession(status, token!);
      currentStatus.value = status;
      isStatusOpen.value = false;
      Fluttertoast.showToast(msg: response['message'] ?? 'Status updated to $status');
      print('Status updated: $status');
      if (status == 'online') {
        _startCallPolling();
      } else {
        stopCallPolling();
      }
    } catch (e) {
      print('Status change error: $e');
      Fluttertoast.showToast(msg: 'Failed to update status: $e');
    } finally {
      isSessionLoading.value = false;
    }
  }

  void handleCallStatusChange(String status, Map<String, dynamic> callData) {
    print('Call status changed: $status, data: $callData');
    if (status == 'ended' || status == 'declined') {
      incomingCall.value = null;
      showIncomingCall.value = false;
      isCallAccepting.value = false;
    }
  }

  void dispose() {
    print('Disposing MentorController...');
    stopCallPolling();
    _ringtonePlayer.dispose();
    isLoading.dispose();
    isError.dispose();
    errorMessage.dispose();
    mentorData.dispose();
    isSessionLoading.dispose();
    isStatusOpen.dispose();
    currentStatus.dispose();
    incomingCall.dispose();
    isCallAccepting.dispose();
    showIncomingCall.dispose();
    isRingtoneLoaded.dispose();
  }
}