// import 'dart:async';
// import 'package:flutter/material.dart';
// import 'mentor_dashboard_api.dart';
// import 'package:flutter_webrtc/flutter_webrtc.dart';

// class IncomingCall extends StatefulWidget {
//   final Map<String, dynamic>? incomingCall;
//   final bool showVideoCall;
//   final Function(String, Map<String, dynamic>)? onCallStatusChange;

//   const IncomingCall({
//     super.key,
//     this.incomingCall,
//     required this.showVideoCall,
//     this.onCallStatusChange,
//   });

//   @override
//   _IncomingCallState createState() => _IncomingCallState();
// }

// class _IncomingCallState extends State<IncomingCall> with TickerProviderStateMixin {
//   String callState = 'idle';
//   String error = '';
//   int callDuration = 0;
//   Timer? callTimer;
//   bool isMuted = false;
//   bool isVideoOff = false;

//   // WebRTC state
//   RTCPeerConnection? _peerConnection;
//   MediaStream? _localStream;
//   MediaStream? _remoteStream;
//   final RTCVideoRenderer _localRenderer = RTCVideoRenderer();
//   final RTCVideoRenderer _remoteRenderer = RTCVideoRenderer();

//   @override
//   void initState() {
//     super.initState();
//     _localRenderer.initialize();
//     _remoteRenderer.initialize();
//     if (widget.showVideoCall && widget.incomingCall != null && widget.incomingCall!['token'] != null) {
//       _joinRoom(widget.incomingCall!['token']);
//     }
//   }

//   void startCallTimer() {
//     callTimer = Timer.periodic(Duration(seconds: 1), (timer) {
//       setState(() {
//         callDuration++;
//       });
//     });
//   }

//   void stopCallTimer() {
//     callTimer?.cancel();
//     callTimer = null;
//   }

//  String formatDuration(int seconds) {
//   final mins = (seconds ~/ 60).toString().padLeft(2, '0');
//   final secs = (seconds % 60).toString().padLeft(2, '0');
//   return '$mins:$secs';
// }


//   Future<void> _initializeWebRTC() async {
//     final configuration = {
//       'iceServers': [
//         {'urls': 'stun:stun.l.google.com:19302'},
//       ]
//     };
//     _peerConnection = await createPeerConnection(configuration);
//     _localStream = await navigator.mediaDevices.getUserMedia({
//       'audio': true,
//       'video': true,
//     });
//     _localRenderer.srcObject = _localStream;

//     _localStream!.getTracks().forEach((track) {
//       _peerConnection!.addTrack(track, _localStream!);
//     });

//     _peerConnection!.onAddStream = (stream) {
//       setState(() {
//         _remoteStream = stream;
//         _remoteRenderer.srcObject = stream;
//       });
//     };

//     _peerConnection!.onIceCandidate = (candidate) {
//       // Handle ICE candidates (send to signaling server)
//     };
//   }

//   Future<void> _joinRoom(String token) async {
//     setState(() {
//       callState = 'connecting';
//     });
//     try {
//       await _initializeWebRTC();
//       // Note: Replace with actual signaling logic for LiveKit or WebRTC
//       setState(() {
//         callState = 'connected';
//       });
//       startCallTimer();
//       widget.onCallStatusChange?.call('connected', {});
//     } catch (e) {
//       setState(() {
//         error = 'Failed to connect: $e';
//         callState = 'error';
//       });
//       widget.onCallStatusChange?.call('error', {});
//     }
//   }

//   Future<void> endCall() async {
//     if (widget.incomingCall?['room_name'] != null) {
//       await MentorDashboardApi().endCall(widget.incomingCall!['room_name']);
//     }
//     await _peerConnection?.close();
//     await _localStream?.dispose();
//     await _remoteStream?.dispose();
//     stopCallTimer();
//     setState(() {
//       callState = 'ended';
//     });
//     widget.onCallStatusChange?.call('ended', {});
//   }

//   Future<void> toggleMute() async {
//     if (_localStream != null) {
//       _localStream!.getAudioTracks().forEach((track) {
//         track.enabled = !track.enabled;
//       });
//       setState(() {
//         isMuted = !isMuted;
//       });
//     }
//   }

//   Future<void> toggleVideo() async {
//     if (_localStream != null) {
//       _localStream!.getVideoTracks().forEach((track) {
//         track.enabled = !track.enabled;
//       });
//       setState(() {
//         isVideoOff = !isVideoOff;
//       });
//     }
//   }

//   @override
//   void dispose() {
//     _peerConnection?.close();
//     _localStream?.dispose();
//     _remoteStream?.dispose();
//     _localRenderer.dispose();
//     _remoteRenderer.dispose();
//     stopCallTimer();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     if (!widget.showVideoCall || widget.incomingCall == null) return SizedBox.shrink();

//     if (callState == 'connecting') {
//       return Scaffold(
//         backgroundColor: Colors.black87,
//         body: Center(
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               CircularProgressIndicator(color: Colors.indigo),
//               SizedBox(height: 16),
//               Text('Connecting to call...', style: TextStyle(color: Colors.white)),
//               if (error.isNotEmpty)
//                 Padding(
//                   padding: EdgeInsets.only(top: 16),
//                   child: Text(error, style: TextStyle(color: Colors.red)),
//                 ),
//               SizedBox(height: 16),
//               ElevatedButton(
//                 onPressed: endCall,
//                 child: Text('Cancel'),
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor: Colors.red,
//                   foregroundColor: Colors.white,
//                 ),
//               ),
//             ],
//           ),
//         ),
//       );
//     }

//     if (callState == 'connected') {
//       return Scaffold(
//         backgroundColor: Colors.black87,
//         body: Column(
//           children: [
//             // Header
//             Container(
//               padding: EdgeInsets.all(16),
//               color: Colors.grey[800],
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Text(
//                     'Student ID: ${widget.incomingCall!['student_id']}',
//                     style: TextStyle(color: Colors.white),
//                   ),
//                   Text(
//                     'Duration: ${formatDuration(callDuration)}',
//                     style: TextStyle(color: Colors.white),
//                   ),
//                 ],
//               ),
//             ),
//             // Video feeds
//             Expanded(
//               child: Stack(
//                 children: [
//                   RTCVideoView(_remoteRenderer, mirror: false),
//                   Positioned(
//                     bottom: 16,
//                     right: 16,
//                     child: Container(
//                       width: 100,
//                       height: 100,
//                       child: RTCVideoView(_localRenderer, mirror: true),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//             // Controls
//             Container(
//               padding: EdgeInsets.all(16),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: [
//                   IconButton(
//                     onPressed: toggleMute,
//                     icon: Icon(isMuted ? Icons.mic_off : Icons.mic),
//                     color: isMuted ? Colors.red : Colors.white,
//                   ),
//                   IconButton(
//                     onPressed: toggleVideo,
//                     icon: Icon(isVideoOff ? Icons.videocam_off : Icons.videocam),
//                     color: isVideoOff ? Colors.red : Colors.white,
//                   ),
//                   IconButton(
//                     onPressed: endCall,
//                     icon: Icon(Icons.call_end),
//                     color: Colors.red,
//                   ),
//                 ],
//               ),
//             ),
//           ],
//         ),
//       );
//     }

//     if (callState == 'ended' || callState == 'error') {
//       return Scaffold(
//         backgroundColor: Colors.black87,
//         body: Center(
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               Text(
//                 callState == 'error' ? 'Call Failed' : 'Call Ended',
//                 style: TextStyle(fontSize: 24, color: Colors.white),
//               ),
//               if (error.isNotEmpty)
//                 Padding(
//                   padding: EdgeInsets.only(top: 8),
//                   child: Text(error, style: TextStyle(color: Colors.red)),
//                 ),
//               SizedBox(height: 16),
//               ElevatedButton(
//                 onPressed: () => widget.onCallStatusChange?.call('ended', {}),
//                 child: Text('Close'),
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor: Colors.indigo,
//                   foregroundColor: Colors.white,
//                 ),
//               ),
//             ],
//           ),
//         ),
//       );
//     }

//     return SizedBox.shrink();
//   }
// }