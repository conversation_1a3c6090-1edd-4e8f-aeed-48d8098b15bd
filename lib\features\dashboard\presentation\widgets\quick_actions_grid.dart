import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/models/menu_models.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/logger.dart';

class QuickActionsGrid extends StatelessWidget {
  final List<MenuItem> menuItems;
  final bool isLoading;

  const QuickActionsGrid({
    super.key,
    required this.menuItems,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return _buildLoadingGrid();
    }

    if (menuItems.isEmpty) {
      return _buildEmptyState();
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.2,
      ),
      itemCount: menuItems.length,
      itemBuilder: (context, index) {
        final menuItem = menuItems[index];
        return _buildActionCard(context, menuItem, index);
      },
    );
  }

  Widget _buildActionCard(BuildContext context, MenuItem menuItem, int index) {
    return GestureDetector(
      onTap: () => _handleActionTap(context, menuItem),
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppTheme.borderColor),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _getMenuIcon(menuItem.iconName),
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
              ),
              
              const SizedBox(height: 12),
              
              Text(
                menuItem.name,
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    ).animate(delay: Duration(milliseconds: 100 * index))
        .fadeIn()
        .scale(begin: const Offset(0.8, 0.8));
  }

  Widget _buildLoadingGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.2,
      ),
      itemCount: 6,
      itemBuilder: (context, index) {
        return Container(
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: AppTheme.borderColor),
          ),
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ).animate(delay: Duration(milliseconds: 100 * index))
            .fadeIn()
            .shimmer();
      },
    );
  }

  Widget _buildEmptyState() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.apps_outlined,
              size: 48,
              color: AppTheme.textTertiary,
            ),
            const SizedBox(height: 16),
            Text(
              'No quick actions available',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textTertiary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleActionTap(BuildContext context, MenuItem menuItem) {
    AppLogger.userAction('Quick action tapped', {
      'title': menuItem.name,
      'path': menuItem.routePath,
    });

    if (menuItem.routePath.isNotEmpty) {
      context.go(menuItem.routePath);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${menuItem.name} feature coming soon!'),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      );
    }
  }

  IconData _getMenuIcon(String? iconName) {
    switch (iconName?.toLowerCase()) {
      case 'dashboard':
        return Icons.dashboard_outlined;
      case 'students':
      case 'student':
        return Icons.people_outline;
      case 'courses':
      case 'course':
        return Icons.book_outlined;
      case 'assignments':
      case 'assignment':
        return Icons.assignment_outlined;
      case 'tests':
      case 'test':
        return Icons.quiz_outlined;
      case 'results':
      case 'result':
        return Icons.assessment_outlined;
      case 'attendance':
        return Icons.event_available_outlined;
      case 'reports':
      case 'report':
        return Icons.analytics_outlined;
      case 'settings':
        return Icons.settings_outlined;
      case 'profile':
        return Icons.person_outline;
      case 'notifications':
        return Icons.notifications_outlined;
      case 'calendar':
        return Icons.calendar_today_outlined;
      case 'messages':
        return Icons.message_outlined;
      case 'library':
        return Icons.library_books_outlined;
      case 'fees':
        return Icons.payment_outlined;
      case 'transport':
        return Icons.directions_bus_outlined;
      case 'faculty':
        return Icons.school_outlined;
      case 'admin':
        return Icons.admin_panel_settings_outlined;
      default:
        return Icons.apps_outlined;
    }
  }
}
