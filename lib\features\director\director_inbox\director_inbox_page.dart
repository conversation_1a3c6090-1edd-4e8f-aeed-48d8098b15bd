import 'package:flutter/material.dart';
import '../../../core/widgets/base_page.dart';
import '../../common/presentation/pages/feature_placeholder_page.dart';

class DirectorInboxPage extends StatelessWidget {
  const DirectorInboxPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const FeaturePlaceholderPage(
      title: 'Director Inbox',
      subtitle: 'Manage your messages and notifications',
      breadcrumbs: ['Dashboard', 'Director', 'Director Inbox'],
      featureName: 'Director Inbox',
    );
  }
}
