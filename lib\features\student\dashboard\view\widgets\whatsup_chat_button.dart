import 'dart:async';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class WhatsAppChatButton extends StatefulWidget {
  final String phoneNumber;
  final String message;
  final bool showDescription;

  const WhatsAppChatButton({
    Key? key,
    this.phoneNumber = '919600780738',
    this.message = '',
    this.showDescription = true,
  }) : super(key: key);

  @override
  State<WhatsAppChatButton> createState() => _WhatsAppChatButtonState();
}

class _WhatsAppChatButtonState extends State<WhatsAppChatButton>
    with SingleTickerProviderStateMixin {
  bool isVisible = false;
  bool hasShownInitial = false;
  bool isHovered = false;

  late AnimationController _controller;
  late Animation<Offset> _offsetAnimation;

  @override
  void initState() {
    super.initState();

    // Tooltip animation
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _offsetAnimation = Tween<Offset>(
      begin: const Offset(0.2, 0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    // Show tooltip for 2s initially
    if (widget.showDescription && !hasShownInitial) {
      setState(() => isVisible = true);
      _controller.forward();
      Timer(const Duration(seconds: 2), () {
        if (mounted) {
          _controller.reverse();
          setState(() {
            isVisible = false;
            hasShownInitial = true;
          });
        }
      });
    }
  }

  String get formattedNumber {
    final pn = widget.phoneNumber;
    return '+${pn.substring(0, 2)} ${pn.substring(2, 7)} ${pn.substring(7)}';
  }

  String get whatsappLink {
    final base = 'https://wa.me/${widget.phoneNumber}';
    return widget.message.isNotEmpty
        ? '$base?text=${Uri.encodeComponent(widget.message)}'
        : base;
  }

  void _openWhatsApp() async {
    final url = Uri.parse(whatsappLink);
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 16,
      right: 16,
      child: Stack(
        alignment: Alignment.centerRight,
        children: [
          // Tooltip
          if (isVisible)
            SlideTransition(
              position: _offsetAnimation,
              child: FadeTransition(
                opacity: _controller,
                child: Container(
                  width: 260,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.2),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.15),
                        blurRadius: 8,
                        offset: const Offset(2, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Icon(Icons.chat_bubble_outline,
                              color: Color(0xFF25D366)),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Need instant help?',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                                const SizedBox(height: 2),
                                const Text(
                                  "We're available 24/7 on WhatsApp",
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.black54,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    color: Colors.grey.withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Text(
                                    formattedNumber,
                                    style: const TextStyle(
                                      fontSize: 12,
                                      fontFamily: 'monospace',
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          // Floating Button with hover scale
          MouseRegion(
            onEnter: (_) {
              if (widget.showDescription) {
                setState(() {
                  isHovered = true;
                  isVisible = true;
                  _controller.forward();
                });
              }
            },
            onExit: (_) {
              if (widget.showDescription) {
                setState(() {
                  isHovered = false;
                  isVisible = false;
                  _controller.reverse();
                });
              }
            },
            child: GestureDetector(
              onTap: _openWhatsApp,
              child: AnimatedScale(
                scale: isHovered ? 1.1 : 1.0,
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeOut,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    color: const Color(0xFF25D366),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.15),
                        blurRadius: 14,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Image.network(
                      "https://upload.wikimedia.org/wikipedia/commons/6/6b/WhatsApp.svg",
                      width: 28,
                      height: 28,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
