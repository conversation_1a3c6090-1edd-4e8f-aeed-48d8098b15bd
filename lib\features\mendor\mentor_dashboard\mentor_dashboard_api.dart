import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../core/config/app_config.dart';
import '../../../core/services/token_service.dart';

class MentorDashboardApi {
  static const String _baseUrl = AppConfig.baseUrl;
  final TokenService _tokenService = TokenService();

  /// Get headers with authentication token
  Future<Map<String, String>> _getAuthHeaders() async {
    final token = await _tokenService.getToken();
    final headers = Map<String, String>.from(AppConfig.defaultHeaders);

    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  /// Fetch mentor dashboard data with authentication
  Future<Map<String, dynamic>> fetchDirectorMentorDashboard() async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/mentor-dashboard'),
        headers: headers,
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else if (response.statusCode == 401) {
        // Token might be expired, try to refresh
        final isValid = await _tokenService.validateAndRefreshToken();
        if (isValid) {
          // Retry with new token
          final newHeaders = await _getAuthHeaders();
          final retryResponse = await http.get(
            Uri.parse('$_baseUrl/mentor-dashboard'),
            headers: newHeaders,
          ).timeout(const Duration(seconds: 10));

          if (retryResponse.statusCode == 200) {
            return jsonDecode(retryResponse.body);
          }
        }
        throw Exception('Authentication failed: ${response.statusCode}');
      } else {
        throw Exception('Failed to fetch dashboard: ${response.statusCode}');
      }
    } catch (e) {
      return {
        'status': e.toString().contains('401') ? 401 : 500,
        'data': {'error': e.toString()}
      };
    }
  }

  /// Update mentor status using /mentor-session endpoint with authentication
  /// The mentor_id is derived from the JWT token on the backend
  /// Removed activeSessionID parameter and replaced with action parameter
  Future<Map<String, dynamic>> updateMentorSession(String action) async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.post(
        Uri.parse('$_baseUrl/mentor-session'),
        headers: headers,
        body: jsonEncode({'action': action}),
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        return {
          'status': 200,
          'data': jsonDecode(response.body)
        };
      } else if (response.statusCode == 401) {
        final isValid = await _tokenService.validateAndRefreshToken();
        if (isValid) {
          final newHeaders = await _getAuthHeaders();
          final retryResponse = await http.post(
            Uri.parse('$_baseUrl/mentor-session'),
            headers: newHeaders,
            body: jsonEncode({'action': action}),
          ).timeout(const Duration(seconds: 10));

          if (retryResponse.statusCode == 200) {
            return {
              'status': 200,
              'data': jsonDecode(retryResponse.body)
            };
          }
        }
        throw Exception('Authentication failed: ${response.statusCode}');
      } else {
        throw Exception('Failed to update session: ${response.statusCode}');
      }
    } catch (e) {
      return {
        'status': e.toString().contains('401') ? 401 : 500,
        'data': {'error': e.toString()}
      };
    }
  }

  /// Update mentor status (online/away/offline) using /mentor-session endpoint
  /// The mentor_id is derived from the JWT token on the backend
  Future<Map<String, dynamic>> updateMentorStatus(String status) async {
    // Use the updateMentorSession method which calls /mentor-session endpoint
    return await updateMentorSession(status);
  }

  /// Poll for incoming calls with authentication
  /// The mentor_id is derived from the JWT token on the backend
  Future<Map<String, dynamic>> pollForCalls() async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/mentor/poll'),
        headers: headers,
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else if (response.statusCode == 401) {
        final isValid = await _tokenService.validateAndRefreshToken();
        if (isValid) {
          final newHeaders = await _getAuthHeaders();
          final retryResponse = await http.get(
            Uri.parse('$_baseUrl/mentor/poll'),
            headers: newHeaders,
          ).timeout(const Duration(seconds: 10));

          if (retryResponse.statusCode == 200) {
            return jsonDecode(retryResponse.body);
          }
        }
        throw Exception('Authentication failed: ${response.statusCode}');
      } else {
        throw Exception('Failed to poll for calls: ${response.statusCode}');
      }
    } catch (e) {
      return {'status': 500, 'data': {'error': e.toString()}};
    }
  }

  /// Accept incoming call with authentication
  /// The mentor_id is derived from the JWT token on the backend
  Future<Map<String, dynamic>> acceptCall(String roomName) async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.post(
        Uri.parse('$_baseUrl/call/accept'),
        headers: headers,
        body: jsonEncode({'room_name': roomName}),
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else if (response.statusCode == 401) {
        final isValid = await _tokenService.validateAndRefreshToken();
        if (isValid) {
          final newHeaders = await _getAuthHeaders();
          final retryResponse = await http.post(
            Uri.parse('$_baseUrl/call/accept'),
            headers: newHeaders,
            body: jsonEncode({'room_name': roomName}),
          ).timeout(const Duration(seconds: 10));

          if (retryResponse.statusCode == 200) {
            return jsonDecode(retryResponse.body);
          }
        }
        throw Exception('Authentication failed: ${response.statusCode}');
      } else {
        throw Exception('Failed to accept call: ${response.statusCode}');
      }
    } catch (e) {
      return {'status': 500, 'data': {'error': e.toString()}};
    }
  }

  /// End call with authentication
  Future<void> endCall(String roomName) async {
    try {
      final headers = await _getAuthHeaders();
      await http.post(
        Uri.parse('$_baseUrl/call/end'),
        headers: headers,
        body: jsonEncode({'room_name': roomName}),
      ).timeout(const Duration(seconds: 10));
    } catch (e) {
      // Log error but don't throw - ending call should be fire-and-forget
      print('Error ending call: $e');
    }
  }
}