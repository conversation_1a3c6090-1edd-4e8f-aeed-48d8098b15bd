import 'package:equatable/equatable.dart';

class StudentDashboardData extends Equatable {
  final Student student;
  final List<Faculty> faculty;
  final List<KotaTeacher> kotaTeachers;

  const StudentDashboardData({
    required this.student,
    required this.faculty,
    required this.kotaTeachers,
  });

  factory StudentDashboardData.fromJson(Map<String, dynamic> json) {
    return StudentDashboardData(
      student: Student.from<PERSON><PERSON>(json['student'] ?? {}),
      faculty: (json['faculty'] as List<dynamic>?)
              ?.map((e) => Faculty.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      kotaTeachers: (json['kota_teachers'] as List<dynamic>?)
              ?.map((e) => KotaTeacher.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  @override
  List<Object?> get props => [student, faculty, kotaTeachers];
}

class Student extends Equatable {
  final String username;
  final String? firstName;
  final String? lastName;
  final String studentEmail;
  final String phone;
  final String course;
  final String centerName;
  final String centerCode;
  final String centerEmail;

  const Student({
    required this.username,
    this.firstName,
    this.lastName,
    required this.studentEmail,
    required this.phone,
    required this.course,
    required this.centerName,
    required this.centerCode,
    required this.centerEmail,
  });

  factory Student.fromJson(Map<String, dynamic> json) {
    return Student(
      username: json['username'] ?? 'Unknown',
      firstName: json['first_name'],
      lastName: json['last_name'],
      studentEmail: json['student_email'] ?? 'Unknown',
      phone: json['phone'] ?? 'Unknown',
      course: json['course'] ?? 'Unknown',
      centerName: json['center_name'] ?? 'Unknown',
      centerCode: json['center_code'] ?? 'Unknown',
      centerEmail: json['center_email'] ?? 'Unknown',
    );
  }

  @override
  List<Object?> get props => [
        username,
        firstName,
        lastName,
        studentEmail,
        phone,
        course,
        centerName,
        centerCode,
        centerEmail
      ];
}

class Faculty extends Equatable {
  final String id;
  final String firstName;
  final String lastName;
  final String email;
  final String phone;

  const Faculty({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phone,
  });

  factory Faculty.fromJson(Map<String, dynamic> json) {
    return Faculty(
      id: json['id'] ?? '',
      firstName: json['first_name'] ?? '',
      lastName: json['last_name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
    );
  }

  @override
  List<Object?> get props => [id, firstName, lastName, email, phone];
}

class KotaTeacher extends Equatable {
  final String id;
  final String firstName;
  final String lastName;
  final String course;
  final String? subject;
  final String? experience;
  final double? rating;

  const KotaTeacher({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.course,
    this.subject,
    this.experience,
    this.rating,
  });

  factory KotaTeacher.fromJson(Map<String, dynamic> json) {
    return KotaTeacher(
      id: json['id'] ?? '',
      firstName: json['first_name'] ?? '',
      lastName: json['last_name'] ?? '',
      course: json['course'] ?? '',
      subject: json['subject'],
      experience: json['experience'],
      rating: (json['rating'] as num?)?.toDouble(),
    );
  }

  @override
  List<Object?> get props => [
        id,
        firstName,
        lastName,
        course,
        subject,
        experience,
        rating,
      ];
}