import 'dart:math';
import 'package:flutter/material.dart';
import '../controller/mentor_dashboard_controller.dart';
import '../model/status_model.dart';
import './incoming_call_overlay.dart';
import './incoming_call_view.dart';

class MentorDashboardView extends StatefulWidget {
  final MentorController controller;

  const MentorDashboardView({super.key, required this.controller});

  @override
  State<MentorDashboardView> createState() => _MentorDashboardViewState();
}

class _MentorDashboardViewState extends State<MentorDashboardView> with TickerProviderStateMixin {
  final _studentProgress = [
    {'name': '<PERSON>', 'progress': 85, 'avatarColor': Colors.pink.shade500},
    {'name': '<PERSON>', 'progress': 72, 'avatarColor': Colors.blue.shade500},
    {'name': '<PERSON>', 'progress': 93, 'avatarColor': Colors.green.shade500},
    {'name': '<PERSON>', 'progress': 68, 'avatarColor': Colors.purple.shade500},
  ];

  final _upcomingSessions = [
    {'title': 'Advanced React Patterns', 'time': 'Today, 4:00 PM', 'status': 'upcoming'},
    {'title': 'State Management Deep Dive', 'time': 'Wed, 4:00 PM', 'status': 'scheduled'},
    {'title': 'Project Review Session', 'time': 'Fri, 4:00 PM', 'status': 'scheduled'},
  ];

  @override
  void initState() {
    super.initState();
    print('Initializing MentorDashboardView...');
    widget.controller.init();
  }

  @override
  void dispose() {
    print('Disposing MentorDashboardView...');
    widget.controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print('Building MentorDashboardView...');
    return Scaffold(
      body: Stack(
        children: [
          // Background Gradient
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Colors.grey.shade50, Colors.indigo.shade50.withOpacity(0.3)],
              ),
            ),
          ),
          // Floating Particles
          ...List.generate(20, (index) {
            return AnimatedPositioned(
              duration: Duration(seconds: Random().nextInt(10) + 10),
              left: Random().nextDouble() * MediaQuery.of(context).size.width,
              top: Random().nextDouble() * MediaQuery.of(context).size.height,
              child: AnimatedContainer(
                duration: Duration(seconds: Random().nextInt(10) + 10),
                width: Random().nextDouble() * 10 + 5,
                height: Random().nextDouble() * 10 + 5,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.indigo.shade200.withOpacity(0.3),
                ),
              ),
            );
          }),
          // Main Content
          SafeArea(
            child: ValueListenableBuilder<bool>(
              valueListenable: widget.controller.isLoading,
              builder: (context, isLoading, _) {
                if (isLoading) {
                  print('Showing loading state...');
                  return _buildLoadingState();
                }
                return ValueListenableBuilder<bool>(
                  valueListenable: widget.controller.isError,
                  builder: (context, isError, _) {
                    if (isError) {
                      print('Showing error state: ${widget.controller.errorMessage.value}');
                      return _buildErrorState();
                    }
                    return ValueListenableBuilder<Map<String, dynamic>?>(
                      valueListenable: widget.controller.mentorData,
                      builder: (context, mentorData, _) {
                        print('Showing success state with mentorData: $mentorData');
                        return _buildSuccessState(mentorData);
                      },
                    );
                  },
                );
              },
            ),
          ),
          // Incoming Call Overlay
          ValueListenableBuilder<bool>(
            valueListenable: widget.controller.showIncomingCall,
            builder: (context, showIncomingCall, _) {
              return ValueListenableBuilder<Map<String, dynamic>?>(
                valueListenable: widget.controller.incomingCall,
                builder: (context, incomingCall, _) {
                  if (showIncomingCall && incomingCall != null) {
                    print('Showing incoming call overlay: $incomingCall');
                    return IncomingCallOverlay(
                      incomingCall: incomingCall,
                      onAccept: widget.controller.handleAcceptCall,
                      onReject: widget.controller.handleRejectCall,
                      isLoading: widget.controller.isCallAccepting.value,
                    );
                  }
                  return const SizedBox.shrink();
                },
              );
            },
          ),
          // Incoming Call View
          ValueListenableBuilder<bool>(
            valueListenable: widget.controller.showIncomingCall,
            builder: (context, showIncomingCall, _) {
              return ValueListenableBuilder<Map<String, dynamic>?>(
                valueListenable: widget.controller.incomingCall,
                builder: (context, incomingCall, _) {
                  if (!showIncomingCall && incomingCall != null) {
                    print('Showing incoming call view: $incomingCall');
                    return IncomingCallView(
                      mentorId: widget.controller.mentorId ?? '',
                      incomingCall: incomingCall,
                      onCallStatusChange: widget.controller.handleCallStatusChange,
                      showVideoCall: true,
                    );
                  }
                  return const SizedBox.shrink();
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Colors.indigo.shade600),
          const SizedBox(height: 16),
          const Text(
            'Building Your Mentor Space',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.grey),
          ),
          const SizedBox(height: 8),
          const Text(
            'Loading your courses, students, and analytics...',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(colors: [Colors.red.shade100, Colors.red.shade50]),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.error, size: 48, color: Colors.red),
            const SizedBox(height: 8),
            const Text(
              'Connection Error',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.red),
            ),
            const SizedBox(height: 8),
            ValueListenableBuilder<String?>(
              valueListenable: widget.controller.errorMessage,
              builder: (context, errorMessage, _) {
                return Text(
                  errorMessage ?? 'Failed to connect to the mentor network.',
                  style: const TextStyle(color: Colors.red),
                );
              },
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => widget.controller.init(),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red.shade600),
              child: const Text('Retry Connection', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSuccessState(Map<String, dynamic>? mentorData) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome, Mentor',
                    style: TextStyle(fontSize: 28, fontWeight: FontWeight.bold, color: Colors.indigo),
                  ),
                  Text(
                    'Your personalized teaching dashboard',
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
              ValueListenableBuilder<String>(
                valueListenable: widget.controller.currentStatus,
                builder: (context, status, _) {
                  if (status == 'online') {
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.green.shade100,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Row(
                        children: [
                          Icon(Icons.circle, color: Colors.green, size: 10),
                          SizedBox(width: 8),
                          Text('Ready for calls', style: TextStyle(color: Colors.green)),
                        ],
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Profile Card
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.9),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.1), blurRadius: 10)],
            ),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  color: Colors.indigo.shade600,
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 40,
                        backgroundColor: Colors.white.withOpacity(0.2),
                        child: Text(
                          '${mentorData?['mendor']?['first_name']?.substring(0, 1) ?? 'M'}${mentorData?['mendor']?['last_name']?.substring(0, 1) ?? ''}',
                          style: const TextStyle(fontSize: 24, color: Colors.white),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${mentorData?['mendor']?['first_name'] ?? 'Mentor'} ${mentorData?['mendor']?['last_name'] ?? ''}',
                            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.white),
                          ),
                          ValueListenableBuilder<String>(
                            valueListenable: widget.controller.currentStatus,
                            builder: (context, status, _) {
                              final currentStatusConfig = StatusOption.options.firstWhere((opt) => opt.value == status);
                              return Container(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: DropdownButton<String>(
                                  value: status,
                                  dropdownColor: Colors.indigo.shade700,
                                  underline: const SizedBox.shrink(),
                                  icon: const Icon(Icons.keyboard_arrow_down, color: Colors.white),
                                  style: const TextStyle(color: Colors.white),
                                  items: StatusOption.options
                                      .map((option) => DropdownMenuItem(
                                            value: option.value,
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Container(
                                                  width: 12,
                                                  height: 12,
                                                  decoration: BoxDecoration(
                                                    color: option.color,
                                                    shape: BoxShape.circle,
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                Text(
                                                  option.label,
                                                  style: const TextStyle(color: Colors.white),
                                                ),
                                              ],
                                            ),
                                          ))
                                      .toList(),
                                  onChanged: (value) {
                                    if (value != null) {
                                      print('Status dropdown changed: $value');
                                      widget.controller.handleStatusChange(value);
                                    }
                                  },
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      _buildProfileInfo('Role', 'Course Mentor', Icons.person),
                      _buildProfileInfo('Email', mentorData?['mendor']?['email'] ?? 'N/A', Icons.email),
                      _buildProfileInfo('Specialization', mentorData?['mendor']?['subject_name'] ?? 'N/A', Icons.book),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          // Stats Grid
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            children: [
              _buildStatCard('Students', '42', '+3 this week', Colors.indigo.shade600, Icons.group),
              _buildStatCard('Courses', '3', null, Colors.blue.shade600, Icons.book),
              _buildStatCard('Sessions', '18', '4 completed', Colors.purple.shade600, Icons.schedule),
              _buildStatCard('Rating', mentorData?['rating']?.toString() ?? '4.8', null, Colors.green.shade600, Icons.star),
            ],
          ),
          const SizedBox(height: 16),
          // Course Details
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.9),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.1), blurRadius: 10)],
            ),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Course Information', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(child: _buildCourseInfo('Current Course', mentorData?['mendor']?['course_name'] ?? 'N/A')),
                    Expanded(child: _buildCourseInfo('Subject', mentorData?['mendor']?['subject_name'] ?? 'N/A')),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(child: _buildCourseInfo('Batch Schedule', 'Mon, Wed, Fri • 4:00-6:00 PM')),
                    Expanded(child: _buildCourseInfo('Next Session', 'Tomorrow • 4:00 PM')),
                  ],
                ),
                const SizedBox(height: 16),
                const Text('Quick Actions', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children: [
                    _buildActionButton('Start Session', Icons.mic, Colors.indigo.shade600),
                    _buildActionButton('Upload Materials', Icons.upload_file, Colors.blue.shade600),
                    _buildActionButton('Record Lecture', Icons.videocam, Colors.purple.shade600),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          // Student Progress and Upcoming Sessions
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.9),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.1), blurRadius: 10)],
                  ),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Student Progress', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      const SizedBox(height: 16),
                      ..._studentProgress.map((student) => _buildStudentProgress(student)),
                      const SizedBox(height: 8),
                      TextButton(
                        onPressed: () => print('View All Students clicked'),
                        child: const Text('View All Students', style: TextStyle(color: Colors.indigo)),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.9),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.1), blurRadius: 10)],
                  ),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Upcoming Sessions', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      const SizedBox(height: 16),
                      ..._upcomingSessions.map((session) => _buildSessionCard(session)),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProfileInfo(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: Colors.indigo),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(label, style: const TextStyle(color: Colors.grey)),
              Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, String? subtitle, Color color, IconData icon) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.1), blurRadius: 10)],
      ),
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(title, style: const TextStyle(color: Colors.grey)),
              Text(value, style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: color)),
              if (subtitle != null) Text(subtitle, style: const TextStyle(color: Colors.grey, fontSize: 12)),
            ],
          ),
          Icon(icon, color: color),
        ],
      ),
    );
  }

  Widget _buildCourseInfo(String label, String value) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.1), blurRadius: 5)],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: const TextStyle(color: Colors.grey)),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Widget _buildActionButton(String label, IconData icon, Color color) {
    return ElevatedButton.icon(
      onPressed: () => print('$label button clicked'),
      icon: Icon(icon),
      label: Text(label),
      style: ElevatedButton.styleFrom(backgroundColor: color),
    );
  }

  Widget _buildStudentProgress(Map<String, dynamic> student) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: student['avatarColor'],
            child: Text(student['name'][0], style: const TextStyle(color: Colors.white)),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(student['name'], style: const TextStyle(fontWeight: FontWeight.bold)),
                LinearProgressIndicator(value: student['progress'] / 100, color: Colors.green),
              ],
            ),
          ),
          Text('${student['progress']}%', style: const TextStyle(color: Colors.grey)),
        ],
      ),
    );
  }

  Widget _buildSessionCard(Map<String, dynamic> session) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: session['status'] == 'upcoming' ? Colors.purple.shade50 : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(session['title'], style: const TextStyle(fontWeight: FontWeight.bold)),
                  Text(session['time'], style: const TextStyle(color: Colors.grey)),
                ],
              ),
              if (session['status'] == 'upcoming')
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.purple.shade100,
                    borderRadius: BorderRadius.circular(999),
                  ),
                  child: const Text('Today', style: TextStyle(color: Colors.purple)),
                ),
            ],
          ),
          if (session['status'] == 'upcoming')
            ElevatedButton(
              onPressed: () => print('Prepare Session clicked for ${session['title']}'),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.purple.shade600),
              child: const Text('Prepare Session', style: TextStyle(color: Colors.white)),
            ),
        ],
      ),
    );
  }
}