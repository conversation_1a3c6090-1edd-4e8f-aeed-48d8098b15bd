import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

class Simulation {
  final String name;
  final String link;

  Simulation({required this.name, required this.link});
}

class Subject {
  final int id;
  final String name;
  final IconData icon;
  final Gradient gradient;
  final List<Simulation> simulations;

  Subject({
    required this.id,
    required this.name,
    required this.icon,
    required this.gradient,
    required this.simulations,
  });
}

final List<Subject> subjects = [
  Subject(
    id: 1,
    name: 'Physics',
    icon: LucideIcons.sparkles,
    gradient: const LinearGradient(
      colors: [Color(0xff0ea5e9), Color(0xff4f46e5)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
    simulations: [
      Simulation(
          name: 'astro-simulations',
          link: 'https://ccnmtl.github.io/astro-simulations/'),
      Simulation(name: 'myphysicslab', link: 'https://www.myphysicslab.com/'),
      Simulation(
          name: 'thephysicsaviary',
          link:
              'https://www.thephysicsaviary.com/Physics/Programs/Labs/find.php'),
      Simulation(name: 'ophysics', link: 'https://ophysics.com/'),
      Simulation(name: 'astro', link: 'https://astro.unl.edu/'),
      Simulation(
          name: 'physics.bu.edu',
          link: 'https://physics.bu.edu/~duffy/HTML5/index.html'),
    ],
  ),
  Subject(
    id: 2,
    name: 'Chemistry',
    icon: LucideIcons.beaker,
    gradient: const LinearGradient(
      colors: [Color(0xfff59e0b), Color(0xffea580c)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
    simulations: [
      Simulation(
          name: 'chemcollective', link: 'https://chemcollective.org/vlabs'),
      Simulation(
          name: 'chemagic', link: 'https://chemagic.org/molecules/amini.html'),
      Simulation(name: 'chemix', link: 'https://chemix.org/'),
    ],
  ),
  Subject(
    id: 3,
    name: 'Biology',
    icon: LucideIcons.microscope,
    gradient: const LinearGradient(
      colors: [Color(0xff10b981), Color(0xff047857)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
    simulations: [],
  ),
  Subject(
    id: 4,
    name: 'Mathematics',
    icon: LucideIcons.calculator,
    gradient: const LinearGradient(
      colors: [Color(0xfff43f5e), Color(0xffbe185d)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
    simulations: [],
  ),
  Subject(
    id: 5,
    name: 'Earth & Space',
    icon: LucideIcons.globe,
    gradient: const LinearGradient(
      colors: [Color(0xff0ea5e9), Color(0xffdc2626)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
    simulations: [],
  ),
];

class VirtualLabsPage extends StatefulWidget {
  const VirtualLabsPage({super.key});

  @override
  State<VirtualLabsPage> createState() => _VirtualLabsPageState();
}

class _VirtualLabsPageState extends State<VirtualLabsPage> {
  Subject? selectedSubject;
  Simulation? selectedSimulation;
  bool isIframeLoading = true;
  late final WebViewController _webViewController;

  @override
  void initState() {
    super.initState();
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (_) {
            setState(() {
              isIframeLoading = false;
            });
          },
        ),
      );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SizedBox.expand(
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 400),
          switchInCurve: Curves.easeOutBack,
          switchOutCurve: Curves.easeInBack,
          child: selectedSimulation != null && selectedSubject != null
              ? simulationView()
              : selectedSubject != null
                  ? simulationListView()
                  : subjectGridView(),
        ),
      ),
    );
  }

  Widget subjectGridView() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xfff0f4f8), Color(0xffd9e2ec)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 40),
      child: Column(
        children: [
          Animate(
            effects: [
              FadeEffect(duration: 600.ms),
              MoveEffect(begin: const Offset(0, -0.3), duration: 600.ms),
            ],
            child: const Text(
              'Virtual Labs',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.w900,
                color: Color(0xff334e68),
              ),
            ),
          ),
          const SizedBox(height: 8),
          Animate(
            effects: [
              FadeEffect(duration: 600.ms, delay: 200.ms),
              MoveEffect(
                  begin: const Offset(0, -0.3),
                  duration: 600.ms,
                  delay: 200.ms),
            ],
            child: const Text(
              'Dive into a universe of discovery. Select a subject to begin your exploration..',
              style: TextStyle(fontSize: 12, color: Color(0xff627d98)),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 32),
          Expanded(
            child: GridView.builder(
              itemCount: subjects.length,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                mainAxisSpacing: 24,
                crossAxisSpacing: 24,
                childAspectRatio: 4 / 3,
              ),
              itemBuilder: (context, index) {
                final subject = subjects[index];
                return Animate(
                  key: ValueKey(subject.id),
                  effects: [
                    FadeEffect(duration: 600.ms, delay: (index * 80).ms),
                    MoveEffect(
                        begin: const Offset(0, 0.2),
                        duration: 600.ms,
                        delay: (index * 80).ms),
                  ],
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        selectedSubject = subject;
                      });
                    },
                    child: MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: SubjectCard(subject: subject),
                    ),
                  ),
                );
              },
            ),
          )
        ],
      ),
    );
  }

  Widget simulationListView() {
    if (selectedSubject == null) return const SizedBox();

    final sims = selectedSubject!.simulations;

    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              gradient: selectedSubject!.gradient,
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            child: Row(
              children: [
                Icon(selectedSubject!.icon, size: 38, color: Colors.white),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    selectedSubject!.name,
                    style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold),
                  ),
                ),
                IconButton(
                  onPressed: () {
                    setState(() {
                      selectedSubject = null;
                    });
                  },
                  icon: const Icon(LucideIcons.x, color: Colors.white),
                  tooltip: 'Back',
                )
              ],
            ),
          ),
          const SizedBox(height: 32),
          sims.isEmpty
              ? Expanded(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(LucideIcons.fileSearch2,
                            size: 64, color: Colors.grey),
                        const SizedBox(height: 16),
                        const Text(
                          'Coming Soon!',
                          style: TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          "We're busy building new simulations for ${selectedSubject!.name}. Please check back later!",
                          style:
                              const TextStyle(fontSize: 18, color: Colors.grey),
                          textAlign: TextAlign.center,
                        )
                      ],
                    ),
                  ),
                )
              : Expanded(
                  child: ListView.builder(
                    itemCount: sims.length,
                    itemBuilder: (context, index) {
                      final sim = sims[index];
                      return Animate(
                        key: ValueKey(sim.name),
                        effects: [
                          FadeEffect(duration: 500.ms, delay: (index * 80).ms),
                          MoveEffect(
                              begin: const Offset(0, 0.2),
                              duration: 500.ms,
                              delay: (index * 80).ms),
                        ],
                        child: Card(
                          margin: const EdgeInsets.symmetric(vertical: 8),
                          elevation: 4,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16)),
                          child: ListTile(
                            leading: const Icon(LucideIcons.playCircle,
                                size: 40, color: Colors.blueGrey),
                            title: Text(sim.name,
                                style: const TextStyle(
                                    fontWeight: FontWeight.w600, fontSize: 18)),
                            onTap: () {
                              setState(() {
                                selectedSimulation = sim;
                                isIframeLoading = true;
                                _webViewController
                                    .loadRequest(Uri.parse(sim.link));
                              });
                            },
                          ),
                        ),
                      );
                    },
                  ),
                ),
        ],
      ),
    );
  }

  Widget simulationView() {
    if (selectedSimulation == null || selectedSubject == null)
      return const SizedBox();

    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                gradient: selectedSubject!.gradient,
                boxShadow: const [
                  BoxShadow(
                      color: Colors.black26,
                      blurRadius: 6,
                      offset: Offset(0, 3))
                ],
              ),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(LucideIcons.chevronLeft,
                        color: Colors.white, size: 28),
                    onPressed: () {
                      setState(() {
                        selectedSimulation = null;
                      });
                    },
                    tooltip: 'Back',
                  ),
                  Expanded(
                    child: Text(
                      selectedSimulation!.name,
                      style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(LucideIcons.externalLink,
                        color: Colors.white, size: 26),
                    onPressed: () async {
                      final url = Uri.parse(selectedSimulation!.link);
                      if (await canLaunchUrl(url)) {
                        await launchUrl(url,
                            mode: LaunchMode.externalApplication);
                      }
                    },
                    tooltip: 'Open in new tab',
                  ),
                ],
              ),
            ),
            Expanded(
              child: Stack(
                children: [
                  WebViewWidget(controller: _webViewController),
                  if (isIframeLoading)
                    Container(
                      color: Colors.white.withOpacity(0.6),
                      child: Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: const [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text('Loading Simulation...',
                                style: TextStyle(fontSize: 18)),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SubjectCard extends StatefulWidget {
  final Subject subject;
  const SubjectCard({super.key, required this.subject});

  @override
  State<SubjectCard> createState() => _SubjectCardState();
}

class _SubjectCardState extends State<SubjectCard>
    with SingleTickerProviderStateMixin {
  bool _hovering = false;
  late final AnimationController _controller;
  late final Animation<double> _scaleAnim;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
        duration: const Duration(milliseconds: 300), vsync: this);
    _scaleAnim = Tween<double>(begin: 1, end: 1.05)
        .animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onHover(bool hover) {
    setState(() {
      _hovering = hover;
    });
    if (hover) {
      _controller.forward();
    } else {
      _controller.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _onHover(true),
      onExit: (_) => _onHover(false),
      child: ScaleTransition(
        scale: _scaleAnim,
        child: Container(
          decoration: BoxDecoration(
            gradient: widget.subject.gradient,
            borderRadius: BorderRadius.circular(24),
            boxShadow: _hovering
                ? [
                    BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 18,
                        offset: const Offset(0, 12))
                  ]
                : [
                    BoxShadow(
                        color: Colors.black.withOpacity(0.15),
                        blurRadius: 10,
                        offset: const Offset(0, 5))
                  ],
          ),
          padding: const EdgeInsets.all(10),
          child: Stack(
            children: [
              Positioned(
                bottom: -8,
                right: -8,
                child: Icon(widget.subject.icon,
                    size: 80, color: Colors.white.withOpacity(0.10)),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(widget.subject.icon, size: 28, color: Colors.white),
                  const Spacer(),
                  Text(
                    widget.subject.name,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
