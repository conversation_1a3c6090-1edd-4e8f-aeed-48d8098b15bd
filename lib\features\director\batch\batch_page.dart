import 'package:flutter/material.dart';
import '../../../core/widgets/base_page.dart';
import '../../common/presentation/pages/feature_placeholder_page.dart';

class BatchPage extends StatelessWidget {
  const BatchPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const FeaturePlaceholderPage(
      title: 'Batch Management',
      subtitle: 'Create and manage student batches',
      breadcrumbs: ['Dashboard', 'Director', 'Batch'],
      featureName: 'Batch Management',
    );
  }
}
