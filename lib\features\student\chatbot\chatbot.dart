import 'dart:async';
import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:intl/intl.dart';
import 'package:sasthra/core/theme/app_theme.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path/path.dart' as path;
import 'package:dio/dio.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:flutter_sound_record/flutter_sound_record.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:http_parser/http_parser.dart';
import 'package:flutter_math_fork/flutter_math.dart';

// Placeholder for your actual config file
import '../../../../core/config/app_config.dart';

// Mock AppConfig for demonstration (replace with your actual AppConfig)

class ChatMessage {
  final String text;
  final String type; // 'user' or 'ai'
  final DateTime timestamp;
  final File? image;
  final File? audio;

  ChatMessage({
    required this.text,
    required this.type,
    required this.timestamp,
    this.image,
    this.audio,
  });
}

class ChatSupport extends StatefulWidget {
  final bool isVisible;
  final VoidCallback onClose;

  const ChatSupport({Key? key, this.isVisible = true, required this.onClose})
      : super(key: key);

  @override
  _ChatSupportState createState() => _ChatSupportState();
}

class _ChatSupportState extends State<ChatSupport>
    with TickerProviderStateMixin {
  final TextEditingController _textController = TextEditingController();
  final List<ChatMessage> _messages = [];
  final ScrollController _scrollController = ScrollController();
  File? _file;
  bool _isLoading = false;
  String? _userId;
  String? _sessionId;
  bool _isInitialized = false;
  late AnimationController _danceAnimationController;
  late Dio _dio;
  late FlutterSoundRecord _audioRecorder;
  bool _isRecording = false;
  Timer? _recordingTimer;

  // Helper lists for file types
  static const _supportedImageExtensions = [
    '.jpg',
    '.jpeg',
    '.png',
    '.heic',
    '.heif'
  ];
  static const _supportedAudioExtensions = [
    '.wav',
    '.mp3',
    '.m4a',
    '.aac',
    '.oga',
    '.ogg'
  ];

  @override
  void initState() {
    super.initState();
    _audioRecorder = FlutterSoundRecord();
    _dio = Dio(BaseOptions(
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 60),
    ));
    _dio.interceptors.add(
      PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        responseBody: true,
        responseHeader: false,
        error: true,
        compact: true,
        maxWidth: 90,
      ),
    );
    _danceAnimationController = AnimationController(
      vsync: this,
      duration: AppConfig.animationDuration,
    );
    _loadUserData();
  }

  @override
  void dispose() {
    _textController.dispose();
    _scrollController.dispose();
    _danceAnimationController.dispose();
    _audioRecorder.dispose();
    _recordingTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userDataString = prefs.getString(AppConfig.userDataKey);
      if (userDataString != null) {
        final userData = json.decode(userDataString);
        _userId = userData['id']?.toString();
      } else {
        _userId = 'user_${DateTime.now().millisecondsSinceEpoch}';
        await prefs.setString(
            AppConfig.userDataKey, json.encode({'id': _userId}));
      }

      _sessionId = prefs.getString(AppConfig.sessionIdKey) ??
          'session_${DateTime.now().millisecondsSinceEpoch}';
      await prefs.setString(AppConfig.sessionIdKey, _sessionId!);
      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load user data: $e')),
      );
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      } else {
        Future.delayed(const Duration(milliseconds: 200), () {
          if (_scrollController.hasClients) {
            _scrollController.animateTo(
              _scrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        });
      }
    });
  }

  Future<void> _startRecording() async {
    final status = await Permission.microphone.status;
    if (status.isPermanentlyDenied) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text(
              'Microphone permission is permanently denied. Please enable it in settings.'),
          action: SnackBarAction(
            label: 'Open Settings',
            onPressed: () => openAppSettings(),
          ),
        ),
      );
      return;
    }

    final permissionStatus = await Permission.microphone.request();
    if (permissionStatus != PermissionStatus.granted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content:
                Text('Microphone permission is required to record audio.')),
      );
      return;
    }

    try {
      final tempDir = await getTemporaryDirectory();
      final audioPath = path.join(tempDir.path, 'audio_recording.m4a');
      await _audioRecorder.start(
        path: audioPath,
        encoder: AudioEncoder.AAC,
      );
      setState(() {
        _isRecording = true;
        _file = null;
      });
      _recordingTimer = Timer(const Duration(seconds: 30), () {
        if (_isRecording) {
          _stopRecording();
        }
      });
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to start recording: $e')),
      );
    }
  }

  Future<void> _stopRecording() async {
    try {
      final String? audioPath = await _audioRecorder.stop();
      _recordingTimer?.cancel();
      setState(() => _isRecording = false);

      if (audioPath != null) {
        setState(() => _file = File(audioPath));
        _handleSubmit();
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to stop recording: $e')),
      );
    }
  }

  Future<void> _pickFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.any,
      );

      if (result != null && result.files.single.path != null) {
        setState(() => _file = File(result.files.single.path!));
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to pick file: $e')),
      );
    }
  }

  MediaType? _getMimeType(String extension) {
    switch (extension.toLowerCase()) {
      case '.jpg':
      case '.jpeg':
        return MediaType('image', 'jpeg');
      case '.png':
        return MediaType('image', 'png');
      case '.heic':
        return MediaType('image', 'heic');
      case '.heif':
        return MediaType('image', 'heif');
      case '.mp3':
        return MediaType('audio', 'mpeg');
      case '.m4a':
        return MediaType('audio', 'mp4');
      case '.aac':
        return MediaType('audio', 'aac');
      case '.wav':
        return MediaType('audio', 'wav');
      case '.ogg':
        return MediaType('audio', 'ogg');
      case '.pdf':
        return MediaType('application', 'pdf');
      case '.txt':
        return MediaType('text', 'plain');
      case '.doc':
      case '.docx':
        return MediaType('application',
            'vnd.openxmlformats-officedocument.wordprocessingml.document');
      default:
        return MediaType('application', 'octet-stream');
    }
  }

  bool get _canSendMessage =>
      _isInitialized &&
      !_isLoading &&
      (_textController.text.isNotEmpty || _file != null);

  Future<void> _handleSubmit() async {
    if (!_canSendMessage) return;

    if (_userId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('User not authenticated.')),
      );
      return;
    }

    final userMessageText = _textController.text.trim();
    final fileToSend = _file;

    if (fileToSend != null) {
      final fileExtension = path.extension(fileToSend.path).toLowerCase();
      final isImage = _supportedImageExtensions.contains(fileExtension);
      final isAudio = _supportedAudioExtensions.contains(fileExtension);

      if (!isImage && !isAudio) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Unsupported file type: "$fileExtension". Please select a supported image or audio file.'),
            duration: const Duration(seconds: 4),
          ),
        );
        setState(() => _file = null);
        return;
      }
    }

    setState(() {
      _isLoading = true;
      _danceAnimationController.repeat();
    });

    String? fileExtension;
    if (fileToSend != null) {
      fileExtension = path.extension(fileToSend.path).toLowerCase();
    }
    final bool isImage = fileExtension != null &&
        _supportedImageExtensions.contains(fileExtension);
    final bool isAudio = fileExtension != null &&
        _supportedAudioExtensions.contains(fileExtension);

    final userMessage = ChatMessage(
      text: userMessageText,
      type: 'user',
      timestamp: DateTime.now(),
      image: isImage ? fileToSend : null,
      audio: isAudio ? fileToSend : null,
    );

    setState(() {
      _messages.add(userMessage);
    });
    _textController.clear();
    _scrollToBottom();

    try {
      final fullUrl = '${AppConfig.apiBaseUrl}${AppConfig.chatBotEndpoint}';
      final Map<String, dynamic> formDataMap = {
        'user_id': _userId!,
      };

      if (userMessageText.isNotEmpty) {
        formDataMap['text'] = userMessageText;
      }

      if (fileToSend != null) {
        final mimeType = _getMimeType(fileExtension!);
        final multipartFile = await MultipartFile.fromFile(
          fileToSend.path,
          filename: path.basename(fileToSend.path),
          contentType: mimeType,
        );

        if (isAudio) {
          formDataMap['audio'] = multipartFile;
        } else if (isImage) {
          formDataMap['image'] = multipartFile;
        }
      }

      final formData = FormData.fromMap(formDataMap);
      final response = await _dio.post(fullUrl, data: formData);
      final Map<String, dynamic> decodedResponse =
          response.data is Map<String, dynamic>
              ? response.data
              : json.decode(response.data.toString());

      if (decodedResponse.containsKey('error')) {
        throw Exception(decodedResponse['error']);
      }

      final String responseText = (decodedResponse['response'] as String?) ??
          'Received an empty response.';
      print('API Response: $responseText'); // Debug log

      // Preprocess the response to clean up invalid LaTeX
      final String formattedText = _preprocessResponse(responseText);

      if (decodedResponse.containsKey('session_id')) {
        _sessionId = decodedResponse['session_id'];
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(AppConfig.sessionIdKey, _sessionId!);
      }

      final aiMessage = ChatMessage(
        text: formattedText,
        type: 'ai',
        timestamp: DateTime.now(),
      );

      if (mounted) {
        setState(() {
          _messages.add(aiMessage);
        });
        _scrollToBottom();
      }
    } catch (e) {
      String errorMessageText = 'An error occurred. Please try again.';
      if (e is DioException) {
        errorMessageText = 'Network Error: ${e.message}.';
        if (e.response?.data != null) {
          errorMessageText +=
              '\nDetails: ${e.response?.data['error'] ?? e.response?.data.toString()}';
        }
      } else {
        errorMessageText = 'Error: ${e.toString()}';
      }

      final errorMessage = ChatMessage(
        text: errorMessageText,
        type: 'ai',
        timestamp: DateTime.now(),
      );
      if (mounted) {
        setState(() {
          _messages.add(errorMessage);
        });
        _scrollToBottom();
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _file = null;
          _danceAnimationController.stop();
        });
        _scrollToBottom();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) {
      return const SizedBox.shrink();
    }
    return Material(
        color: Colors.transparent,
        child: Container(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                spreadRadius: 5,
                blurRadius: 15,
              ),
            ],
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: !_isInitialized
              ? const Center(child: CircularProgressIndicator())
              : Column(
                  children: [
                    _buildHeader(),
                    Expanded(
                      child: _messages.isEmpty
                          ? _buildEmptyState()
                          : _buildMessageList(),
                    ),
                    _buildInputField(),
                  ],
                ),
        ));
  }

  Widget _buildHeader() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppTheme.primaryColor, AppTheme.successColor],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(24, 16, 16, 24),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const SizedBox(width: 40),

// Balance the close button

                  Text(
                    AppConfig.appName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.5,
                    ),
                  ),

                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.close,
                          color: Colors.white, size: 20),
                      onPressed: widget.onClose,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

// Enhanced bot avatar with animation

              AnimatedBuilder(
                animation: _danceAnimationController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: 1.0 + (_danceAnimationController.value * 0.1),
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 2,
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(18),
                        child: Image.asset(
                          'assets/botTwo.gif',
                          fit: BoxFit.cover,
                          errorBuilder: (c, o, s) => const Icon(
                            Icons.smart_toy,
                            size: 40,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: 16),

// Status indicator with animation

              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: _isLoading
                          ? AppTheme.warningColor
                          : AppTheme.successColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _isLoading ? 'Thinking...' : 'Online',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMessageList() {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16.0),
      itemCount: _messages.length,
      itemBuilder: (context, index) {
        final message = _messages[index];
        final isUser = message.type == 'user';

        return Align(
          key: ValueKey(
              'message-$index-${message.timestamp.millisecondsSinceEpoch}'),
          alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 4),
            padding: const EdgeInsets.all(12),
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.75,
            ),
            decoration: BoxDecoration(
              color: isUser ? Colors.deepPurpleAccent : Colors.grey[200],
              borderRadius:
                  BorderRadius.circular(AppConfig.borderRadius).subtract(
                isUser
                    ? const BorderRadius.only(
                        bottomRight: Radius.circular(AppConfig.borderRadius))
                    : const BorderRadius.only(
                        bottomLeft: Radius.circular(AppConfig.borderRadius)),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (message.image != null)
                  Image.file(
                    message.image!,
                    width: 200,
                    height: 200,
                    fit: BoxFit.cover,
                    errorBuilder: (c, o, s) =>
                        const Text('Failed to load image'),
                  ),
                if (message.audio != null)
                  Row(
                    children: [
                      Icon(Icons.audiotrack,
                          color: isUser ? Colors.white : Colors.black),
                      const SizedBox(width: 8),
                      Text(
                        path.basename(message.audio!.path),
                        style: TextStyle(
                            color: isUser ? Colors.white : Colors.black),
                      ),
                    ],
                  ),
                if (message.text.isNotEmpty)
                  if (isUser)
                    Text(
                      message.text,
                      style: const TextStyle(color: Colors.white, fontSize: 16),
                    )
                  else
                    _buildAiMessageContent(message.text),
                const SizedBox(height: 4),
                Align(
                  alignment: Alignment.bottomRight,
                  child: Text(
                    DateFormat('hh:mm a').format(message.timestamp),
                    style: TextStyle(
                      fontSize: 10,
                      color: isUser ? Colors.white70 : Colors.black54,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _preprocessResponse(String response) {
    String cleaned = response
        // SPECIFIC FIX: Corrects the "$   $" error from your API into a standard "$$"
        .replaceAll(RegExp(r'\$\s+\$'), r'$$')
        // Standardizes all newline characters for consistent splitting
        .replaceAll(RegExp(r'\r\n|\r'), '\n')
        // Removes newlines that are directly inside LaTeX delimiters, which can break rendering
        .replaceAllMapped(RegExp(r'(\${1,2})([\s\S]*?)\1'), (match) {
      return match.group(0)!.replaceAll('\n', ' ');
    }).trim();
    return cleaned;
  }

  Widget _buildAiMessageContent(String text) {
    final processedText = _preprocessResponse(text);
    if (processedText.isEmpty) {
      return const Text('Received an empty response.',
          style: TextStyle(color: Colors.grey));
    }

    // Split the entire response into paragraphs. A paragraph is separated by one or more blank lines.
    final paragraphs = processedText.split(RegExp(r'\n\s*\n+'));

    List<Widget> contentWidgets = [];

    for (final paragraph in paragraphs) {
      if (paragraph.trim().isEmpty) continue;

      // Here you can add more markdown-like handling, e.g., for headers or lists.
      // For now, we treat every block as a paragraph with potential LaTeX.
      contentWidgets.add(_buildParagraphWidget(paragraph.trim()));
    }

    if (contentWidgets.isEmpty) {
      return const Text(
        'No content to display',
        style: TextStyle(color: Colors.grey),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: contentWidgets,
    );
  }

// New helper widget to parse a single paragraph that may contain mixed text and LaTeX
  Widget _buildParagraphWidget(String paragraph) {
    List<Widget> inlineWidgets = [];
    // Regex to find all instances of LaTeX, both inline ($) and display ($$)
    final RegExp latexRegex = RegExp(r'(\$\$.*?\$\$)|(\$.*?\$)');
    int lastMatchEnd = 0;

    for (final match in latexRegex.allMatches(paragraph)) {
      // 1. Add the plain text that comes before the LaTeX match
      if (match.start > lastMatchEnd) {
        inlineWidgets.add(Text(
          paragraph.substring(lastMatchEnd, match.start),
          style:
              const TextStyle(color: Colors.black, fontSize: 16, height: 1.5),
        ));
      }

      // 2. Process and add the LaTeX widget
      final latexText = match.group(0)!;
      final bool isDisplayMode = latexText.startsWith(r'$$');
      // Remove the starting and ending delimiters ($ or $$)
      final String cleanedLatex = latexText
          .substring(
              isDisplayMode ? 2 : 1, latexText.length - (isDisplayMode ? 2 : 1))
          .trim();

      if (cleanedLatex.isNotEmpty) {
        inlineWidgets.add(
          Math.tex(
            cleanedLatex,
            mathStyle: isDisplayMode ? MathStyle.display : MathStyle.text,
            textStyle: const TextStyle(fontSize: 16),
            onErrorFallback: (err) {
              // If rendering fails, show the raw LaTeX in red for debugging
              print(
                  "LaTeX Rendering Error: ${err.message} for \"$cleanedLatex\"");
              return Text(
                '[$cleanedLatex]',
                style: const TextStyle(
                    color: Colors.red, fontWeight: FontWeight.bold),
              );
            },
          ),
        );
      }
      lastMatchEnd = match.end;
    }

    // 3. Add any remaining plain text after the last LaTeX match
    if (lastMatchEnd < paragraph.length) {
      inlineWidgets.add(Text(
        paragraph.substring(lastMatchEnd),
        style: const TextStyle(color: Colors.black, fontSize: 16, height: 1.5),
      ));
    }

    // Use a Wrap widget to allow text and inline math to flow together naturally.
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Wrap(
        crossAxisAlignment: WrapCrossAlignment.center,
        runSpacing: 8.0, // Vertical spacing between lines in the wrap
        spacing: 4.0, // Horizontal spacing between elements in the wrap
        children: inlineWidgets,
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.chat_bubble_outline, size: 60, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Hi there!\nHow can I help you today?',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildInputField() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey[200]!)),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_file != null)
              Container(
                padding: const EdgeInsets.all(8),
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(
                      _supportedAudioExtensions.contains(
                              path.extension(_file!.path).toLowerCase())
                          ? Icons.audiotrack
                          : Icons.attach_file,
                      color: Colors.deepPurpleAccent,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        path.basename(_file!.path),
                        style: const TextStyle(color: Colors.black87),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, size: 20),
                      onPressed: () => setState(() => _file = null),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _textController,
                    enabled: _isInitialized,
                    decoration: InputDecoration(
                      hintText:
                          _isRecording ? 'Recording...' : 'Type a message...',
                      border: OutlineInputBorder(
                        borderRadius:
                            BorderRadius.circular(AppConfig.borderRadius * 2),
                        borderSide: BorderSide.none,
                      ),
                      fillColor: Colors.grey[100],
                      filled: true,
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 10),
                    ),
                    onSubmitted: (_) => _handleSubmit(),
                    onChanged: (text) => setState(() {}),
                  ),
                ),
                const SizedBox(width: 8),
                if (!_isRecording && _file == null)
                  IconButton(
                    tooltip: 'Attach a File',
                    icon: const Icon(Icons.attach_file,
                        color: Colors.deepPurpleAccent),
                    onPressed: _isInitialized ? _pickFile : null,
                  ),
                IconButton(
                  tooltip: _isRecording ? 'Stop Recording' : 'Record Voice',
                  icon: Icon(_isRecording ? Icons.stop_circle : Icons.mic),
                  color:
                      _isRecording ? Colors.redAccent : Colors.deepPurpleAccent,
                  onPressed: _isInitialized
                      ? (_isRecording ? _stopRecording : _startRecording)
                      : null,
                ),
                IconButton(
                  tooltip: 'Send Message',
                  icon: const Icon(Icons.send),
                  color: Colors.deepPurpleAccent,
                  onPressed: _canSendMessage ? _handleSubmit : null,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
