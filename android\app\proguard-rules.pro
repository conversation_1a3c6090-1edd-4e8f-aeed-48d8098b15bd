# Flutter specific ProGuard rules

# Keep Flutter app classes
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }

# Keep Kotlin Coroutines
-keepattributes *Annotation*, InnerClasses
-dontnote kotlinx.serialization.SerializationKt
-keep,includedescriptorclasses class com.sasthra.app.**$$serializer { *; }
-keepclassmembers class com.sasthra.app.** {
    *** Companion;
}
-keepclasseswithmembers class com.sasthra.app.** {
    kotlinx.serialization.KSerializer serializer(...); 
}

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep JavaScript interface methods
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# Keep Parcelable implementations
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

# Keep R8 rules
-keepattributes Signature
-keepattributes SourceFile,LineNumberTable
-keepattributes *Annotation*

# Application specific rules
-keep class com.sasthra.app.** { *; }

# Google Play Core library
-keep class com.google.android.play.core.** { *; }