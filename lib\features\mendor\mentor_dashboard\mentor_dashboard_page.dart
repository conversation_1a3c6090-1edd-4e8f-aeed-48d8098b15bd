import 'package:flutter/material.dart';
import 'controller/mentor_dashboard_controller.dart';
import 'model/mentor_model.dart';
import 'view/mentor_dashboard_view.dart';

class MentorDashboardPage extends StatefulWidget {
  const MentorDashboardPage({super.key});

  @override
  State<MentorDashboardPage> createState() => _MentorDashboardPageState();
}

class _MentorDashboardPageState extends State<MentorDashboardPage> {
  late MentorController controller;

  @override
  void initState() {
    super.initState();
    controller = MentorController(model: MentorModel());
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MentorDashboardView(controller: controller);
  }
}